"use strict";
const common_vendor = require("../common/vendor.js");
const utils_helpers = require("../utils/helpers.js");
const utils_apiService = require("../utils/apiService.js");
const _sfc_main = {
  name: "CartonInfoModal",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    },
    imageInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      cartonData: {}
    };
  },
  watch: {
    data: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.cartonData = utils_helpers.deepClone(newVal);
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    /**
     * 编辑基本信息
     */
    editBasicInfo(field, value) {
      this.cartonData[field] = value;
    },
    /**
     * 编辑订单数据
     */
    editOrderData(index, field, value) {
      if (this.cartonData.orderData && index >= 0 && index < this.cartonData.orderData.length) {
        this.cartonData.orderData[index][field] = value;
      }
    },
    /**
     * 确认保存
     */
    async confirm() {
      try {
        utils_helpers.showLoading("正在保存数据...");
        await utils_apiService.apiService.saveCartonInfoData(this.cartonData, this.imageInfo);
        utils_helpers.hideLoading();
        utils_helpers.showSuccess("保存成功！");
        this.$emit("confirm", this.cartonData);
      } catch (error) {
        utils_helpers.hideLoading();
        common_vendor.index.__f__("error", "at components/CartonInfoModal.vue:222", "保存装柜信息表数据失败:", error);
        utils_helpers.showError(error.message || "保存数据时发生错误");
      }
    },
    /**
     * 取消
     */
    cancel() {
      this.$emit("cancel");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.visible
  }, $props.visible ? common_vendor.e({
    b: common_vendor.o([($event) => $data.cartonData.Date = $event.detail.value, ($event) => $options.editBasicInfo("Date", $event.detail.value)]),
    c: $data.cartonData.Date,
    d: common_vendor.o([($event) => $data.cartonData.SalesmanName = $event.detail.value, ($event) => $options.editBasicInfo("SalesmanName", $event.detail.value)]),
    e: $data.cartonData.SalesmanName,
    f: common_vendor.o([($event) => $data.cartonData.CartonNumber = $event.detail.value, ($event) => $options.editBasicInfo("CartonNumber", $event.detail.value)]),
    g: $data.cartonData.CartonNumber,
    h: common_vendor.o([($event) => $data.cartonData.SealNumber = $event.detail.value, ($event) => $options.editBasicInfo("SealNumber", $event.detail.value)]),
    i: $data.cartonData.SealNumber,
    j: $data.cartonData.orderData && $data.cartonData.orderData.length > 0
  }, $data.cartonData.orderData && $data.cartonData.orderData.length > 0 ? {
    k: common_vendor.t($data.cartonData.orderData.length),
    l: common_vendor.f($data.cartonData.orderData, (order, index, i0) => {
      return {
        a: common_vendor.o([($event) => order.OrderNumber = $event.detail.value, ($event) => $options.editOrderData(index, "OrderNumber", $event.detail.value)], index),
        b: order.OrderNumber,
        c: common_vendor.o([($event) => order.CategoryName = $event.detail.value, ($event) => $options.editOrderData(index, "CategoryName", $event.detail.value)], index),
        d: order.CategoryName,
        e: common_vendor.o([($event) => order.Piece = $event.detail.value, ($event) => $options.editOrderData(index, "Piece", $event.detail.value)], index),
        f: order.Piece,
        g: common_vendor.o([($event) => order.Weight = $event.detail.value, ($event) => $options.editOrderData(index, "Weight", $event.detail.value)], index),
        h: order.Weight,
        i: index
      };
    })
  } : {}, {
    m: common_vendor.o([($event) => $data.cartonData.TotalPiece = $event.detail.value, ($event) => $options.editBasicInfo("TotalPiece", $event.detail.value)]),
    n: $data.cartonData.TotalPiece,
    o: common_vendor.o([($event) => $data.cartonData.TotalWeight = $event.detail.value, ($event) => $options.editBasicInfo("TotalWeight", $event.detail.value)]),
    p: $data.cartonData.TotalWeight,
    q: common_vendor.o((...args) => $options.cancel && $options.cancel(...args)),
    r: common_vendor.o((...args) => $options.confirm && $options.confirm(...args))
  }) : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-acfbbe68"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/CartonInfoModal.js.map
