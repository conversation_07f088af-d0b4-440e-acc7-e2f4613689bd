
	/* 装柜放船样登记表确认弹窗样式 */
.confirm-modal-overlay.data-v-4d23352c {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
		padding: 20rpx;
}
.confirm-modal.data-v-4d23352c {
		background: white;
		border-radius: 30rpx;
		width: 100%;
		height: 90vh;
		max-width: 100vw;
		display: flex;
		flex-direction: column;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
		overflow: hidden;
}
.modal-header.data-v-4d23352c {
		padding: 30rpx 40rpx 20rpx;
		border-bottom: 2rpx solid #f0f0f0;
		text-align: center;
		flex-shrink: 0;
		background: white;
		z-index: 10;
}
.modal-title.data-v-4d23352c {
		font-size: 36rpx;
		font-weight: bold;
		color: #2c3e50;
		display: block;
		margin-bottom: 10rpx;
}
.modal-subtitle.data-v-4d23352c {
		font-size: 26rpx;
		color: #666;
		display: block;
}
.modal-content.data-v-4d23352c {
		flex: 1;
		overflow: hidden;
		position: relative;
}

	/* 表格容器样式 */
.table-wrapper.data-v-4d23352c {
		min-width: 1400rpx;
		width: 1400rpx;
		background: white;
}

	/* 固定表头样式 */
.table-header-fixed.data-v-4d23352c {
		display: flex;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		font-weight: bold;
		position: -webkit-sticky;
		position: sticky;
		top: 0;
		z-index: 5;
		border-bottom: 3rpx solid #5a67d8;
		will-change: transform;
		box-sizing: border-box;
}
.header-cell.data-v-4d23352c {
		padding: 25rpx 15rpx;
		text-align: center;
		font-size: 28rpx;
		border-right: 2rpx solid rgba(255, 255, 255, 0.3);
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 80rpx;
		flex-shrink: 0;
		flex-grow: 0;
		box-sizing: border-box;
}
.header-cell.data-v-4d23352c:last-child {
		border-right: none;
}

	/* 列宽定义 */
.date-col.data-v-4d23352c {
		width: 200rpx;
		min-width: 200rpx;
		box-sizing: border-box;
}
.order-col.data-v-4d23352c {
		width: 250rpx;
		min-width: 250rpx;
		box-sizing: border-box;
}
.category-col.data-v-4d23352c {
		width: 300rpx;
		min-width: 300rpx;
		box-sizing: border-box;
}
.salesman-col.data-v-4d23352c {
		width: 200rpx;
		min-width: 200rpx;
		box-sizing: border-box;
}
.place-col.data-v-4d23352c {
		width: 200rpx;
		min-width: 200rpx;
		box-sizing: border-box;
}
.comment-col.data-v-4d23352c {
		width: 250rpx;
		min-width: 250rpx;
		box-sizing: border-box;
}

	/* 表格主体 */
.table-body.data-v-4d23352c {
		background: white;
}
.table-row.data-v-4d23352c {
		display: flex;
		border-bottom: 2rpx solid #f0f0f0;
		min-height: 100rpx;
}
.table-row.data-v-4d23352c:last-child {
		border-bottom: none;
}
.table-row.data-v-4d23352c:nth-child(even) {
		background: #fafafa;
}
.table-row.data-v-4d23352c:hover {
		background: #f0f8ff;
}

	/* 输入框容器 */
.input-wrapper.data-v-4d23352c {
		padding: 15rpx 10rpx;
		display: flex;
		align-items: center;
		border-right: 2rpx solid #e0e0e0;
}
.input-wrapper.data-v-4d23352c:last-child {
		border-right: none;
}

	/* 输入框样式 */
.table-input.data-v-4d23352c {
		width: 100%;
		padding: 20rpx 15rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 10rpx;
		font-size: 26rpx;
		text-align: center;
		background: white;
		transition: all 0.3s ease;
		min-height: 60rpx;
}
.table-input.data-v-4d23352c:focus {
		background: #f8f9ff;
		border-color: #667eea;
		outline: none;
		box-shadow: 0 0 10rpx rgba(102, 126, 234, 0.3);
}
.table-input.data-v-4d23352c::-webkit-input-placeholder {
		color: #999;
		font-size: 24rpx;
}
.table-input.data-v-4d23352c::placeholder {
		color: #999;
		font-size: 24rpx;
}

	/* 底部按钮区域 */
.modal-footer.data-v-4d23352c {
		padding: 30rpx 40rpx;
		border-top: 2rpx solid #f0f0f0;
		display: flex;
		gap: 30rpx;
		justify-content: center;
		flex-shrink: 0;
		background: white;
		box-shadow: 0 -5rpx 15rpx rgba(0, 0, 0, 0.1);
}
.cancel-btn.data-v-4d23352c,
	.confirm-btn.data-v-4d23352c {
		flex: 1;
		max-width: 300rpx;
		padding: 30rpx 0;
		border-radius: 25rpx;
		font-size: 32rpx;
		font-weight: bold;
		border: none;
		cursor: pointer;
		transition: all 0.3s ease;
}
.cancel-btn.data-v-4d23352c {
		background: #f5f5f5;
		color: #666;
		border: 2rpx solid #ddd;
}
.cancel-btn.data-v-4d23352c:hover {
		background: #e0e0e0;
		transform: translateY(-2rpx);
}
.confirm-btn.data-v-4d23352c {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
}
.confirm-btn.data-v-4d23352c:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.4);
}
