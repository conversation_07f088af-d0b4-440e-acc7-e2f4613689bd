"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_warehouseUsers = require("../../utils/warehouseUsers.js");
const _sfc_main = {
  data() {
    return {
      loginForm: {
        username: "",
        password: ""
      },
      showPassword: false,
      isLoading: false
    };
  },
  computed: {
    /**
     * 检查是否可以提交表单
     */
    canSubmit() {
      return this.loginForm.username.trim() !== "" && this.loginForm.password.trim() !== "";
    },
    /**
     * 密码输入框类型
     */
    passwordInputType() {
      return this.showPassword ? "text" : "password";
    },
    /**
     * 密码切换图标
     */
    passwordToggleIcon() {
      return this.showPassword ? "🙈" : "👁️";
    }
  },
  onLoad() {
    common_vendor.index.__f__("log", "at pages/warehouseLogin/warehouseLogin.vue:105", "📦 仓库管理登录页面加载");
    if (utils_warehouseUsers.warehouseUserManager.checkLoginStatus()) {
      common_vendor.index.__f__("log", "at pages/warehouseLogin/warehouseLogin.vue:109", "✅ 用户已登录，直接跳转到仓库管理");
      this.navigateToWarehouse();
    }
  },
  methods: {
    /**
     * 切换密码显示状态
     */
    togglePassword() {
      common_vendor.index.__f__("log", "at pages/warehouseLogin/warehouseLogin.vue:118", "切换密码显示状态，当前状态:", this.showPassword);
      this.showPassword = !this.showPassword;
      common_vendor.index.__f__("log", "at pages/warehouseLogin/warehouseLogin.vue:120", "切换后状态:", this.showPassword);
      this.$forceUpdate();
    },
    /**
     * 处理登录
     */
    async handleLogin() {
      if (!this.canSubmit || this.isLoading) {
        return;
      }
      this.isLoading = true;
      try {
        const result = utils_warehouseUsers.warehouseUserManager.login(
          this.loginForm.username,
          this.loginForm.password
        );
        if (result.success) {
          common_vendor.index.showToast({
            title: "登录成功",
            icon: "success",
            duration: 1500
          });
          setTimeout(() => {
            this.navigateToWarehouse();
          }, 1500);
        } else {
          common_vendor.index.showToast({
            title: result.message || "登录失败",
            icon: "error",
            duration: 2e3
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/warehouseLogin/warehouseLogin.vue:165", "❌ 登录过程中发生错误:", error);
        common_vendor.index.showToast({
          title: "登录失败，请重试",
          icon: "error",
          duration: 2e3
        });
      } finally {
        this.isLoading = false;
      }
    },
    /**
     * 跳转到仓库管理主页面
     */
    navigateToWarehouse() {
      common_vendor.index.redirectTo({
        url: "/pages/index/index",
        success: () => {
          common_vendor.index.__f__("log", "at pages/warehouseLogin/warehouseLogin.vue:183", "✅ 跳转到仓库管理页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/warehouseLogin/warehouseLogin.vue:186", "❌ 跳转到仓库管理页面失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "error"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.isLoading,
    b: $data.loginForm.username,
    c: common_vendor.o(($event) => $data.loginForm.username = $event.detail.value),
    d: $options.passwordInputType,
    e: !$data.showPassword,
    f: $data.isLoading,
    g: $data.loginForm.password,
    h: common_vendor.o(($event) => $data.loginForm.password = $event.detail.value),
    i: common_vendor.t($options.passwordToggleIcon),
    j: $data.showPassword ? 1 : "",
    k: common_vendor.o((...args) => $options.togglePassword && $options.togglePassword(...args)),
    l: $data.isLoading
  }, $data.isLoading ? {} : {}, {
    m: $data.isLoading ? 1 : "",
    n: !$options.canSubmit || $data.isLoading,
    o: common_vendor.o((...args) => $options.handleLogin && $options.handleLogin(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-6c98fa9f"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/warehouseLogin/warehouseLogin.js.map
