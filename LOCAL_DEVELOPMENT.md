# 本地开发环境配置指南

## 概述
本指南帮助您在本地环境中运行项目，避免与服务器端口冲突。

## 端口分配
- **服务器生产环境**: 后端端口 3000
- **本地开发环境**: 后端端口 3001
- **前端H5开发**: 自动分配端口（通常8080）

## 配置文件说明

### 1. 后端配置
- `server/.env`: 生产环境配置（端口3000）
- `server/.env.local`: 本地开发配置（端口3001）

### 2. 前端配置
前端已自动适配本地开发环境：
- 生产环境: `https://www.mls2005.top`
- 本地开发: `http://localhost:3001`

## 启动步骤

### 1. 启动后端（本地开发模式）
```bash
cd server
npm install
npm run dev:local:watch
```

### 2. 启动前端
```bash
cd front
npm install
npm run dev:h5
```

## 访问地址
- 后端API: http://localhost:3001
- 前端H5: 通过uni-app开发工具或浏览器访问

## 数据库连接
本地开发环境仍然连接远程数据库（************），无需额外配置。

## 文件存储
本地开发使用相对路径存储文件：
- 仓库图片: `./uploads/warehouse`
- 订单图片: `./uploads/order`
- 临时文件: `./uploads/temp`

## 注意事项
1. 确保本地3001端口未被占用
2. 本地开发不需要nginx代理
3. 微信小程序测试需要在开发者工具中关闭域名校验
4. 生产环境配置保持不变，不影响服务器运行

## 故障排除
如果遇到端口冲突，可以修改 `server/.env.local` 中的 `PORT` 值，并相应更新前端配置。
