/**
 * URL配置工具
 * 统一管理API基础URL，避免重复代码
 */

/**
 * 获取API基础URL
 * 根据不同环境自动选择合适的URL
 * @returns {string} API基础URL
 */
export function getApiBaseUrl() {
	// 检查是否在微信小程序环境
	// #ifdef MP-WEIXIN
	return 'https://www.mls2005.top';
	// #endif
	
	// 检查是否在H5环境
	// #ifdef H5
	if (typeof window !== 'undefined' && window.location.protocol === 'https:') {
		return 'https://www.mls2005.top';
	}
	return 'http://localhost:3001';
	// #endif
	
	// 其他环境默认使用HTTPS
	return 'https://www.mls2005.top';
}

/**
 * 获取完整的API URL
 * @param {string} path API路径，如 '/api/auth/login'
 * @returns {string} 完整的API URL
 */
export function getApiUrl(path) {
	const baseUrl = getApiBaseUrl();
	// 确保路径以 / 开头
	const normalizedPath = path.startsWith('/') ? path : `/${path}`;
	return `${baseUrl}${normalizedPath}`;
}

/**
 * 获取图片URL
 * @param {string} imagePath 图片路径
 * @param {string|number} imageId 图片ID（可选）
 * @returns {string} 图片URL
 */
export function getImageUrl(imagePath, imageId = null) {
	// 如果是完整的HTTP/HTTPS URL，直接返回
	if (imagePath && (imagePath.startsWith('http://') || imagePath.startsWith('https://'))) {
		return imagePath;
	}

	const baseUrl = getApiBaseUrl();

	// 优先使用imageId通过API访问
	if (imageId) {
		return `${baseUrl}/api/images/file/${imageId}`;
	}

	// 对于本地文件路径（如微信小程序临时文件），直接返回
	if (imagePath && (imagePath.startsWith('file://') || imagePath.startsWith('/') || imagePath.includes('tmp'))) {
		return imagePath;
	}

	return imagePath || '';
}

/**
 * 获取上传URL
 * @returns {string} 上传API URL
 */
export function getUploadUrl() {
	return getApiUrl('/api/images/upload');
}

/**
 * 获取历史图片URL
 * @param {string} imagePath 图片路径
 * @returns {string} 历史图片URL
 */
export function getHistoryImageUrl(imagePath) {
	if (!imagePath) return '';

	// 处理不同类型的路径
	let processedPath = imagePath;

	// 如果是完整的Windows路径，转换为相对路径
	if (imagePath.startsWith('C:\\MLS\\Warehouse_Img\\')) {
		processedPath = imagePath.replace('C:\\MLS\\Warehouse_Img\\', '');
	} else if (imagePath.startsWith('C:/MLS/Warehouse_Img/')) {
		processedPath = imagePath.replace('C:/MLS/Warehouse_Img/', '');
	} else if (imagePath.startsWith('C:\\Warehouse_Img\\')) {
		processedPath = imagePath.replace('C:\\Warehouse_Img\\', '');
	} else if (imagePath.startsWith('C:/Warehouse_Img/')) {
		processedPath = imagePath.replace('C:/Warehouse_Img/', '');
	}

	// 将Windows路径分隔符转换为URL路径分隔符
	const urlPath = processedPath.replace(/\\/g, '/');

	// 对路径进行URL编码，但保留路径分隔符
	const encodedPath = urlPath.split('/').map(segment => encodeURIComponent(segment)).join('/');

	return getApiUrl(`/api/history/image/${encodedPath}`);
}

/**
 * 环境检测工具
 */
export const ENV = {
	// 是否为微信小程序环境
	isWeixin: () => {
		// #ifdef MP-WEIXIN
		return true;
		// #endif
		return false;
	},
	
	// 是否为H5环境
	isH5: () => {
		// #ifdef H5
		return true;
		// #endif
		return false;
	},
	
	// 是否为HTTPS环境
	isHttps: () => {
		// #ifdef H5
		return typeof window !== 'undefined' && window.location.protocol === 'https:';
		// #endif
		// #ifdef MP-WEIXIN
		return true; // 小程序默认使用HTTPS
		// #endif
		return false;
	},
	
	// 是否为生产环境
	isProduction: () => {
		return ENV.isWeixin() || ENV.isHttps();
	}
};

export default {
	getApiBaseUrl,
	getApiUrl,
	getImageUrl,
	getUploadUrl,
	getHistoryImageUrl,
	ENV
};
