<template>
	<view class="image-picker">
		<!-- 多图片预览区域 -->
		<view class="images-preview" v-if="selectedImages.length > 0">
			<view class="images-grid">
				<view class="image-item" v-for="(image, index) in selectedImages" :key="index">
					<image :src="image" mode="aspectFit" class="preview-image"></image>
					<view class="image-remove" @click="removeImage(index)">
						<text class="remove-icon">×</text>
					</view>
					<view class="image-index">{{index + 1}}</view>
				</view>
				<!-- 添加更多图片按钮 -->
				<view class="add-more-btn" v-if="selectedImages.length < 9" @click="chooseImage">
					<text class="add-icon">+</text>
					<text class="add-text">添加图片</text>
				</view>
			</view>
			<view class="images-info">
				<text class="info-text">已选择 {{selectedImages.length}} 张图片</text>
				<text class="limit-text">最多可选择9张</text>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-else>
			<text class="empty-icon">📷</text>
			<text class="empty-text">请选择或拍摄图片</text>
			<text class="limit-text">支持多张图片，最多9张</text>
		</view>

		<!-- 操作按钮区域 -->
		<view class="action-buttons">
			<button class="btn btn-camera" @click="takePhoto">
				<text class="btn-icon">📷</text>
				<text class="btn-text">拍照</text>
			</button>
			<button class="btn btn-album" @click="chooseImage">
				<text class="btn-icon">🖼️</text>
				<text class="btn-text">相册</text>
			</button>
		</view>
	</view>
</template>

<script>
	import { showError, showSuccess } from '@/utils/helpers.js';

	export default {
		name: 'ImagePicker',
		props: {
			// 当前选中的图片数组 (Vue 3 v-model)
			modelValue: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
				selectedImages: []
			}
		},
		watch: {
			modelValue: {
				handler(newVal) {
					this.selectedImages = Array.isArray(newVal) ? [...newVal] : [];
				},
				immediate: true
			}
		},
		methods: {
			/**
			 * 拍照
			 */
			takePhoto() {
				console.log('📷 点击拍照按钮');
				const remainingCount = 9 - this.selectedImages.length;
				if (remainingCount <= 0) {
					showError('最多只能选择9张图片');
					return;
				}

				// H5环境下的特殊处理
				// #ifdef H5
				console.log('🌐 H5环境：调用相册选择');
				this.chooseImage();
				// #endif

				// 非H5环境使用拍照功能
				// #ifndef H5
				uni.chooseImage({
					count: 1, // 拍照只能一张
					sourceType: ['camera'],
					success: (res) => {
						console.log('📷 拍照成功:', res);
						this.handleImageSelect(res);
					},
					fail: (err) => {
						console.error('📷 拍照失败:', err);
						showError('拍照失败');
					}
				});
				// #endif
			},

			/**
			 * 从相册选择图片
			 */
			chooseImage() {
				console.log('🖼️ 点击相册按钮');
				const remainingCount = 9 - this.selectedImages.length;
				if (remainingCount <= 0) {
					showError('最多只能选择9张图片');
					return;
				}

				uni.chooseImage({
					count: remainingCount, // 根据剩余数量动态设置
					sourceType: ['album'],
					success: (res) => {
						console.log('🖼️ 相册选择成功:', res);
						this.handleImageSelect(res);
					},
					fail: (err) => {
						console.error('🖼️ 选择图片失败:', err);
						showError('选择图片失败');
					}
				});
			},

			/**
			 * 处理图片选择结果
			 */
			handleImageSelect(res) {
				console.log('🔄 处理图片选择结果:', res);
				if (res.tempFilePaths && res.tempFilePaths.length > 0) {
					// 添加新选择的图片到现有数组
					const newImages = [...this.selectedImages, ...res.tempFilePaths];

					// 确保不超过9张
					if (newImages.length > 9) {
						showError('最多只能选择9张图片');
						return;
					}

					this.selectedImages = newImages;
					console.log('✅ 选择图片成功，当前共:', this.selectedImages.length, '张');

					// 触发父组件事件 (Vue 3 v-model)
					this.$emit('update:modelValue', this.selectedImages);
					this.$emit('change', this.selectedImages);
					console.log('📤 已发送事件到父组件');

					showSuccess(`成功添加 ${res.tempFilePaths.length} 张图片`);
				} else {
					console.log('❌ 图片选择失败，没有获取到文件路径');
					showError('图片选择失败');
				}
			},

			/**
			 * 移除指定索引的图片
			 */
			removeImage(index) {
				console.log('🗑️ 移除图片，索引:', index);
				this.selectedImages.splice(index, 1);

				// 触发父组件事件
				this.$emit('update:modelValue', this.selectedImages);
				this.$emit('change', this.selectedImages);

				showSuccess('图片已移除');
			}
		}
	}
</script>

<style scoped>
	.image-picker {
		margin-bottom: 40rpx;
	}

	/* 多图片预览区域 */
	.images-preview {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 40rpx;
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
	}

	.images-grid {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		margin-bottom: 20rpx;
	}

	.image-item {
		position: relative;
		width: 200rpx;
		height: 200rpx;
		border-radius: 15rpx;
		overflow: hidden;
	}

	.preview-image {
		width: 100%;
		height: 100%;
		border-radius: 15rpx;
	}

	.image-remove {
		position: absolute;
		top: 10rpx;
		right: 10rpx;
		width: 40rpx;
		height: 40rpx;
		background: rgba(255, 0, 0, 0.8);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.remove-icon {
		color: white;
		font-size: 24rpx;
		font-weight: bold;
	}

	.image-index {
		position: absolute;
		bottom: 10rpx;
		left: 10rpx;
		background: rgba(0, 0, 0, 0.6);
		color: white;
		padding: 5rpx 10rpx;
		border-radius: 10rpx;
		font-size: 20rpx;
	}

	.add-more-btn {
		width: 200rpx;
		height: 200rpx;
		border: 2rpx dashed #cccccc;
		border-radius: 15rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: #fafafa;
	}

	.add-icon {
		font-size: 60rpx;
		color: #999999;
		margin-bottom: 10rpx;
	}

	.add-text {
		font-size: 24rpx;
		color: #999999;
	}

	.images-info {
		text-align: center;
	}

	.info-text {
		font-size: 28rpx;
		color: #666666;
		display: block;
		margin-bottom: 10rpx;
	}

	.limit-text {
		font-size: 24rpx;
		color: #999999;
		display: block;
	}

	.file-name {
		font-size: 24rpx;
		color: #999999;
		display: block;
	}

	/* 空状态 */
	.empty-state {
		background: rgba(255, 255, 255, 0.9);
		border-radius: 20rpx;
		padding: 80rpx 30rpx;
		text-align: center;
		margin-bottom: 40rpx;
		border: 2rpx dashed #cccccc;
	}

	.empty-icon {
		font-size: 120rpx;
		margin-bottom: 30rpx;
		opacity: 0.6;
		display: block;
	}

	.empty-text {
		font-size: 32rpx;
		color: #999999;
		display: block;
		margin-bottom: 10rpx;
	}

	/* 操作按钮 */
	.action-buttons {
		display: flex;
		justify-content: space-between;
		gap: 30rpx;
	}

	.btn {
		flex: 1;
		background: rgba(255, 255, 255, 0.9);
		border: none;
		border-radius: 15rpx;
		padding: 40rpx 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
		transition: all 0.3s ease;
	}

	.btn:active {
		transform: translateY(2rpx);
		box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.2);
	}

	.btn-icon {
		font-size: 60rpx;
		margin-bottom: 15rpx;
		display: block;
	}

	.btn-text {
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
	}

	.btn-camera {
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
	}

	.btn-album {
		background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
	}
</style>
