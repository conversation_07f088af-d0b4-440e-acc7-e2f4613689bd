
.container.data-v-1cf27b2a {
		padding: 40rpx;
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

	/* 头部样式 */
.header.data-v-1cf27b2a {
		text-align: center;
		margin-bottom: 60rpx;
}
.title.data-v-1cf27b2a {
		font-size: 48rpx;
		font-weight: bold;
		color: #ffffff;
		display: block;
		margin-bottom: 20rpx;
}
.subtitle.data-v-1cf27b2a {
		font-size: 28rpx;
		color: #e8e8e8;
		display: block;
}

	/* 图片预览区域 */
.image-preview.data-v-1cf27b2a {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 40rpx;
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}
.preview-image.data-v-1cf27b2a {
		width: 100%;
		height: 400rpx;
		border-radius: 15rpx;
		margin-bottom: 20rpx;
}
.image-info.data-v-1cf27b2a {
		text-align: center;
}
.info-text.data-v-1cf27b2a {
		font-size: 28rpx;
		color: #666666;
		display: block;
		margin-bottom: 10rpx;
}
.file-name.data-v-1cf27b2a {
		font-size: 24rpx;
		color: #999999;
		display: block;
}

	/* 空状态 */
.empty-state.data-v-1cf27b2a {
		background: rgba(255, 255, 255, 0.9);
		border-radius: 20rpx;
		padding: 80rpx 30rpx;
		text-align: center;
		margin-bottom: 40rpx;
		border: 2rpx dashed #cccccc;
}
.empty-icon.data-v-1cf27b2a {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 30rpx;
		opacity: 0.6;
}
.empty-text.data-v-1cf27b2a {
		font-size: 32rpx;
		color: #999999;
		display: block;
}

	/* 操作按钮 */
.action-buttons.data-v-1cf27b2a {
		display: flex;
		justify-content: space-between;
		margin-bottom: 40rpx;
		gap: 30rpx;
}
.btn.data-v-1cf27b2a {
		flex: 1;
		background: rgba(255, 255, 255, 0.9);
		border: none;
		border-radius: 15rpx;
		padding: 40rpx 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
		transition: all 0.3s ease;
}
.btn.data-v-1cf27b2a:active {
		transform: translateY(2rpx);
		box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.2);
}
.btn-icon.data-v-1cf27b2a {
		font-size: 60rpx;
		margin-bottom: 15rpx;
		display: block;
}
.btn-text.data-v-1cf27b2a {
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
}
.btn-camera.data-v-1cf27b2a {
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}
.btn-album.data-v-1cf27b2a {
		background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

	/* 今日历史按钮区域 */
.history-section.data-v-1cf27b2a {
		margin-bottom: 40rpx;
		display: flex;
		justify-content: center;
}
.history-btn.data-v-1cf27b2a {
		width: 300rpx;
		height: 100rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border: none;
		border-radius: 50rpx;
		color: white;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		font-weight: bold;
		box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.4);
		transition: all 0.3s ease;
}
.history-btn.data-v-1cf27b2a:active {
		transform: translateY(2rpx);
		box-shadow: 0 5rpx 15rpx rgba(102, 126, 234, 0.4);
}
.history-btn .btn-icon.data-v-1cf27b2a {
		margin-right: 15rpx;
		font-size: 32rpx;
}
.history-btn .btn-text.data-v-1cf27b2a {
		font-size: 28rpx;
		font-weight: bold;
}

	/* 识别状态提示 */
.recognition-status.data-v-1cf27b2a {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.6);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
}
.status-content.data-v-1cf27b2a {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 60rpx 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
}
.loading-spinner.data-v-1cf27b2a {
		width: 60rpx;
		height: 60rpx;
		border: 6rpx solid #f3f3f3;
		border-top: 6rpx solid #667eea;
		border-radius: 50%;
		animation: spin-1cf27b2a 1s linear infinite;
		margin-bottom: 30rpx;
}
@keyframes spin-1cf27b2a {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.status-text.data-v-1cf27b2a {
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
}



	/* 加载状态 */
.loading-overlay.data-v-1cf27b2a {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
}
.loading-content.data-v-1cf27b2a {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 60rpx;
		text-align: center;
		min-width: 300rpx;
}
.loading-spinner.data-v-1cf27b2a {
		width: 60rpx;
		height: 60rpx;
		border: 6rpx solid #f3f3f3;
		border-top: 6rpx solid #667eea;
		border-radius: 50%;
		animation: spin-1cf27b2a 1s linear infinite;
		margin: 0 auto 30rpx;
}
@keyframes spin-1cf27b2a {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.loading-text.data-v-1cf27b2a {
		font-size: 28rpx;
		color: #666666;
		display: block;
}

	/* 历史记录按钮 - 暂时隐藏 */
	/* .history-btn {
		width: 240rpx;
		height: 100rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		border: none;
		border-radius: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 40rpx auto;
		font-size: 30rpx;
		font-weight: bold;
		box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.4);
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;
	}

	.history-btn::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		transition: left 0.5s ease;
	}

	.history-btn:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
	}

	.history-btn:active::before {
		left: 100%;
	}

	.history-btn .btn-icon {
		margin-right: 15rpx;
		font-size: 36rpx;
		animation: bounce 2s infinite;
	}

	.history-btn .btn-text {
		font-size: 30rpx;
		font-weight: bold;
	} */

	/* @keyframes bounce {
		0%, 20%, 50%, 80%, 100% {
			transform: translateY(0);
		}
		40% {
			transform: translateY(-4rpx);
		}
		60% {
			transform: translateY(-2rpx);
		}
	} */

	/* 历史记录弹窗 - 暂时隐藏
	/*
	.history-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.6);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
		backdrop-filter: blur(10rpx);
	}

	.history-content {
		width: 95%;
		max-width: 750rpx;
		max-height: 80%;
		background: white;
		border-radius: 40rpx;
		overflow: hidden;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
		animation: modalSlideIn 0.3s ease-out;
	}

	@keyframes modalSlideIn {
		from {
			opacity: 0;
			transform: translateY(50rpx) scale(0.9);
		}
		to {
			opacity: 1;
			transform: translateY(0) scale(1);
		}
	}

	.history-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 50rpx 40rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		position: relative;
	}

	.history-header::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 4rpx;
		background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
	}

	.history-title {
		font-size: 42rpx;
		font-weight: bold;
		color: white;
		display: flex;
		align-items: center;
	}

	.history-title::before {
		content: '📋';
		margin-right: 20rpx;
		font-size: 48rpx;
	}

	.close-btn {
		width: 80rpx;
		height: 80rpx;
		background: rgba(255, 255, 255, 0.2);
		color: white;
		border: 2rpx solid rgba(255, 255, 255, 0.3);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		font-weight: bold;
		transition: all 0.3s ease;
	}

	.close-btn:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.95);
	}

	.history-list {
		max-height: 800rpx;
		padding: 0;
		overflow-y: auto;
	}

	.history-item {
		padding: 40rpx;
		border-bottom: 1rpx solid #f0f0f0;
		margin: 0;
		background: white;
		transition: background-color 0.2s ease;
		position: relative;
	}

	.history-item:hover {
		background: #f8f9fa;
	}

	.history-item:last-child {
		border-bottom: none;
	}

	.history-item::before {
		content: '';
		position: absolute;
		left: 0;
		top: 0;
		bottom: 0;
		width: 8rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	.history-info {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		margin-left: 30rpx;
	}

	.history-name {
		font-size: 28rpx;
		color: #666;
		font-weight: normal;
		display: flex;
		align-items: center;
	}

	.history-name::before {
		content: '📷';
		margin-right: 12rpx;
		font-size: 28rpx;
	}

	.history-result {
		font-size: 38rpx;
		color: #2c3e50;
		font-weight: bold;
		background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
		padding: 20rpx 25rpx;
		border-radius: 20rpx;
		display: inline-block;
		border-left: 6rpx solid #667eea;
		box-shadow: 0 6rpx 15rpx rgba(102, 126, 234, 0.15);
		letter-spacing: 2rpx;
	}

	.history-date {
		font-size: 26rpx;
		color: #999;
		display: flex;
		align-items: center;
	}

	.history-date::before {
		content: '🕒';
		margin-right: 10rpx;
		font-size: 26rpx;
	}

	.no-history {
		text-align: center;
		padding: 120rpx 40rpx;
		color: #999;
		font-size: 32rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 30rpx;
	}

	.no-history::before {
		content: '📝';
		font-size: 100rpx;
		opacity: 0.5;
	}
	*/

	/* 装柜放船样登记表确认弹窗样式 */
.confirm-modal-overlay.data-v-1cf27b2a {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
		padding: 20rpx;
}
.confirm-modal.data-v-1cf27b2a {
		background: white;
		border-radius: 30rpx;
		width: 100%;
		height: 90vh;
		max-width: 100vw;
		display: flex;
		flex-direction: column;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
		overflow: hidden;
}
.modal-header.data-v-1cf27b2a {
		padding: 30rpx 40rpx 20rpx;
		border-bottom: 2rpx solid #f0f0f0;
		text-align: center;
		flex-shrink: 0;
		background: white;
		z-index: 10;
}
.modal-title.data-v-1cf27b2a {
		font-size: 36rpx;
		font-weight: bold;
		color: #2c3e50;
		display: block;
		margin-bottom: 10rpx;
}
.modal-subtitle.data-v-1cf27b2a {
		font-size: 26rpx;
		color: #666;
		display: block;
}
.modal-content.data-v-1cf27b2a {
		flex: 1;
		overflow: hidden;
		position: relative;
}

	/* 表格容器样式 */
.table-wrapper.data-v-1cf27b2a {
		min-width: 1450rpx;
		width: 1450rpx;
		background: white;
}

	/* 固定表头样式 */
.table-header-fixed.data-v-1cf27b2a {
		display: flex;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		font-weight: bold;
		position: -webkit-sticky;
		position: sticky;
		top: 0;
		z-index: 5;
		border-bottom: 3rpx solid #5a67d8;
}
.header-cell.data-v-1cf27b2a {
		padding: 25rpx 15rpx;
		text-align: center;
		font-size: 28rpx;
		border-right: 2rpx solid rgba(255, 255, 255, 0.3);
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 80rpx;
}
.header-cell.data-v-1cf27b2a:last-child {
		border-right: none;
}

	/* 列宽定义 */
.date-col.data-v-1cf27b2a {
		width: 200rpx;
		min-width: 200rpx;
}
.order-col.data-v-1cf27b2a {
		width: 250rpx;
		min-width: 250rpx;
}
.category-col.data-v-1cf27b2a {
		width: 300rpx;
		min-width: 300rpx;
}
.salesman-col.data-v-1cf27b2a {
		width: 200rpx;
		min-width: 200rpx;
}
.place-col.data-v-1cf27b2a {
		width: 200rpx;
		min-width: 200rpx;
}
.comment-col.data-v-1cf27b2a {
		width: 250rpx;
		min-width: 250rpx;
}

	/* 表格主体 */
.table-body.data-v-1cf27b2a {
		background: white;
}
.table-row.data-v-1cf27b2a {
		display: flex;
		border-bottom: 2rpx solid #f0f0f0;
		min-height: 100rpx;
}
.table-row.data-v-1cf27b2a:last-child {
		border-bottom: none;
}
.table-row.data-v-1cf27b2a:nth-child(even) {
		background: #fafafa;
}
.table-row.data-v-1cf27b2a:hover {
		background: #f0f8ff;
}

	/* 输入框容器 */
.input-wrapper.data-v-1cf27b2a {
		padding: 15rpx 10rpx;
		display: flex;
		align-items: center;
		border-right: 2rpx solid #e0e0e0;
}
.input-wrapper.data-v-1cf27b2a:last-child {
		border-right: none;
}

	/* 输入框样式 */
.table-input.data-v-1cf27b2a {
		width: 100%;
		padding: 20rpx 15rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 10rpx;
		font-size: 26rpx;
		text-align: center;
		background: white;
		transition: all 0.3s ease;
		min-height: 60rpx;
}
.table-input.data-v-1cf27b2a:focus {
		background: #f8f9ff;
		border-color: #667eea;
		outline: none;
		box-shadow: 0 0 10rpx rgba(102, 126, 234, 0.3);
}
.table-input.data-v-1cf27b2a::-webkit-input-placeholder {
		color: #999;
		font-size: 24rpx;
}
.table-input.data-v-1cf27b2a::placeholder {
		color: #999;
		font-size: 24rpx;
}

	/* 不同类型输入框的特殊样式 */
.date-input.data-v-1cf27b2a {
		text-align: center;
}
.order-input.data-v-1cf27b2a {
		text-align: center;
		font-weight: bold;
}
.category-input.data-v-1cf27b2a {
		text-align: left;
}
.salesman-input.data-v-1cf27b2a {
		text-align: center;
}
.place-input.data-v-1cf27b2a {
		text-align: center;
		font-weight: bold;
}
.comment-input.data-v-1cf27b2a {
		text-align: left;
}

	/* 底部按钮区域 */
.modal-footer.data-v-1cf27b2a {
		padding: 30rpx 40rpx;
		border-top: 2rpx solid #f0f0f0;
		display: flex;
		gap: 30rpx;
		justify-content: center;
		flex-shrink: 0;
		background: white;
		box-shadow: 0 -5rpx 15rpx rgba(0, 0, 0, 0.1);
}
.cancel-btn.data-v-1cf27b2a,
	.confirm-btn.data-v-1cf27b2a {
		flex: 1;
		max-width: 300rpx;
		padding: 30rpx 0;
		border-radius: 25rpx;
		font-size: 32rpx;
		font-weight: bold;
		border: none;
		cursor: pointer;
		transition: all 0.3s ease;
}
.cancel-btn.data-v-1cf27b2a {
		background: #f5f5f5;
		color: #666;
		border: 2rpx solid #ddd;
}
.cancel-btn.data-v-1cf27b2a:hover {
		background: #e0e0e0;
		transform: translateY(-2rpx);
}
.confirm-btn.data-v-1cf27b2a {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
}
.confirm-btn.data-v-1cf27b2a:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.4);
}

	/* 滚动条样式优化 */
.modal-content.data-v-1cf27b2a::-webkit-scrollbar {
		width: 8rpx;
		height: 8rpx;
}
.modal-content.data-v-1cf27b2a::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 10rpx;
}
.modal-content.data-v-1cf27b2a::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 10rpx;
}
.modal-content.data-v-1cf27b2a::-webkit-scrollbar-thumb:hover {
		background: #a8a8a8;
}

	/* 订单表格滚动条样式 */
.orders-table-scroll.data-v-1cf27b2a::-webkit-scrollbar {
		width: 6rpx;
		height: 6rpx;
}
.orders-table-scroll.data-v-1cf27b2a::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 6rpx;
}
.orders-table-scroll.data-v-1cf27b2a::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 6rpx;
}
.orders-table-scroll.data-v-1cf27b2a::-webkit-scrollbar-thumb:hover {
		background: #a8a8a8;
}

	/* 横向滚动容器滚动条样式 */
.orders-table-horizontal-scroll.data-v-1cf27b2a::-webkit-scrollbar {
		width: 6rpx;
		height: 8rpx;
}
.orders-table-horizontal-scroll.data-v-1cf27b2a::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 8rpx;
}
.orders-table-horizontal-scroll.data-v-1cf27b2a::-webkit-scrollbar-thumb {
		background: #667eea;
		border-radius: 8rpx;
}
.orders-table-horizontal-scroll.data-v-1cf27b2a::-webkit-scrollbar-thumb:hover {
		background: #5a67d8;
}

	/* 装柜信息表确认弹窗样式 */
.carton-modal-overlay.data-v-1cf27b2a {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
}
.carton-modal.data-v-1cf27b2a {
		background: white;
		border-radius: 20rpx;
		width: 90%;
		max-width: 800rpx;
		height: 80vh;
		display: flex;
		flex-direction: column;
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}
.carton-modal-content.data-v-1cf27b2a {
		flex: 1;
		overflow-y: auto;
		padding: 30rpx;
		display: flex;
		flex-direction: column;
}
.carton-info-section.data-v-1cf27b2a,
	.carton-totals-section.data-v-1cf27b2a {
		margin-bottom: 30rpx;
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 10rpx;
}
.carton-orders-section.data-v-1cf27b2a {
		margin-bottom: 30rpx;
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 10rpx;
}
.orders-table-container.data-v-1cf27b2a {
		border-radius: 8rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		width: 100%;
}

	/* 横向滚动容器 */
.orders-table-horizontal-scroll.data-v-1cf27b2a {
		width: 100%;
		white-space: nowrap;
}

	/* 表格包装器 - 设置最小宽度确保横向滚动 */
.orders-table-wrapper.data-v-1cf27b2a {
		display: inline-block;
		min-width: 1150rpx; /* 总列宽：250+300+200+300=1050rpx，加上边距和边框 */
		width: 100%;
}
.section-title.data-v-1cf27b2a {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		padding-bottom: 10rpx;
		border-bottom: 2rpx solid #e9ecef;
}
.info-row.data-v-1cf27b2a {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
}
.info-label.data-v-1cf27b2a {
		width: 120rpx;
		font-size: 28rpx;
		color: #666;
		font-weight: 500;
}
.info-input.data-v-1cf27b2a {
		flex: 1;
		padding: 15rpx 20rpx;
		border: 2rpx solid #e9ecef;
		border-radius: 8rpx;
		font-size: 28rpx;
		background: white;
}
.info-input.data-v-1cf27b2a:focus {
		border-color: #667eea;
		outline: none;
}

	/* 订单表格样式 */
.orders-table-header.data-v-1cf27b2a {
		display: flex;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 8rpx 8rpx 0 0;
		overflow: hidden;
		min-width: 1150rpx; /* 与包装器宽度保持一致 */
}
.order-header-cell.data-v-1cf27b2a {
		padding: 20rpx 10rpx;
		text-align: center;
		font-size: 26rpx;
		font-weight: bold;
		color: white;
		border-right: 2rpx solid rgba(255, 255, 255, 0.2);
		white-space: nowrap; /* 防止文字换行 */
}
.order-header-cell.data-v-1cf27b2a:last-child {
		border-right: none;
}

	/* 纵向滚动容器的包装器 */
.orders-table-scroll-container.data-v-1cf27b2a {
		border: 2rpx solid #e9ecef;
		border-top: none;
		border-radius: 0 0 8rpx 8rpx;
		background: white;
}
.orders-table-scroll.data-v-1cf27b2a {
		height: 600rpx; /* 固定高度，确保有足够空间显示多行数据 */
		background: white;
}
.orders-table-body.data-v-1cf27b2a {
		background: white;
}
.order-row.data-v-1cf27b2a {
		display: flex;
		border-bottom: 2rpx solid #e9ecef;
}
.order-row.data-v-1cf27b2a:last-child {
		border-bottom: none;
}
.order-cell.data-v-1cf27b2a {
		padding: 10rpx;
		border-right: 2rpx solid #e9ecef;
		white-space: nowrap; /* 防止内容换行 */
}
.order-cell.data-v-1cf27b2a:last-child {
		border-right: none;
}
.order-input.data-v-1cf27b2a {
		width: 100%;
		padding: 15rpx 10rpx;
		border: 1rpx solid #ddd;
		border-radius: 6rpx;
		font-size: 24rpx;
		text-align: center;
		background: white;
}
.order-input.data-v-1cf27b2a:focus {
		border-color: #667eea;
		outline: none;
}

	/* 固定列宽定义 - 确保横向滚动时列宽一致 */
.order-col-fixed.data-v-1cf27b2a {
		width: 250rpx;
		min-width: 250rpx;
		max-width: 250rpx;
}
.category-col-fixed.data-v-1cf27b2a {
		width: 300rpx;
		min-width: 300rpx;
		max-width: 300rpx;
}
.piece-col-fixed.data-v-1cf27b2a {
		width: 200rpx;
		min-width: 200rpx;
		max-width: 200rpx;
}
.weight-col-fixed.data-v-1cf27b2a {
		width: 300rpx;
		min-width: 300rpx;
		max-width: 300rpx;
}

	/* 保留原有的flex布局作为备用 */
.order-col.data-v-1cf27b2a {
		flex: 1.2;
}
.category-col.data-v-1cf27b2a {
		flex: 1.5;
}
.piece-col.data-v-1cf27b2a,
	.weight-col.data-v-1cf27b2a {
		flex: 1;
}

	/* 进度条样式 */
.progress-info.data-v-1cf27b2a {
		margin-top: 20rpx;
		text-align: center;
}
.progress-text.data-v-1cf27b2a {
		font-size: 24rpx;
		color: #666666;
		display: block;
		margin-bottom: 10rpx;
}
.progress-bar.data-v-1cf27b2a {
		width: 200rpx;
		height: 8rpx;
		background: rgba(255, 255, 255, 0.3);
		border-radius: 4rpx;
		margin: 0 auto;
		overflow: hidden;
}
.progress-fill.data-v-1cf27b2a {
		height: 100%;
		background: #007aff;
		border-radius: 4rpx;
		transition: width 0.3s ease;
}

	/* 顶部用户信息样式 */
.header.data-v-1cf27b2a {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 40rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		margin-bottom: 30rpx;
}
.title-section.data-v-1cf27b2a {
		flex: 1;
}
.title.data-v-1cf27b2a {
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 8rpx;
}
.subtitle.data-v-1cf27b2a {
		font-size: 24rpx;
		opacity: 0.8;
}
.user-section.data-v-1cf27b2a {
		display: flex;
		align-items: center;
		gap: 20rpx;
}
.user-info.data-v-1cf27b2a {
		text-align: right;
}
.user-name.data-v-1cf27b2a {
		font-size: 28rpx;
		font-weight: bold;
		display: block;
		margin-bottom: 4rpx;
}
.user-dept.data-v-1cf27b2a {
		font-size: 22rpx;
		opacity: 0.8;
		display: block;
}
.logout-btn.data-v-1cf27b2a {
		width: 60rpx;
		height: 60rpx;
		background: rgba(255, 255, 255, 0.2);
		border: none;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
}
.logout-btn.data-v-1cf27b2a:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.95);
}
.logout-icon.data-v-1cf27b2a {
		font-size: 28rpx;
		color: white;
}
