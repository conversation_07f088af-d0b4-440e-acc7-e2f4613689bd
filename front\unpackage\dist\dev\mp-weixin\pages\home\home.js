"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {};
  },
  methods: {
    /**
     * 跳转到仓库管理（先跳转到登录页面）
     */
    goToWarehouse() {
      common_vendor.index.navigateTo({
        url: "/pages/warehouseLogin/warehouseLogin",
        success: () => {
          common_vendor.index.__f__("log", "at pages/home/<USER>", "跳转到仓库管理登录页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/home/<USER>", "跳转到仓库管理登录页面失败:", err);
          common_vendor.index.showToast({
            title: "跳转失败",
            icon: "error"
          });
        }
      });
    },
    /**
     * 跳转到订单管理
     */
    goToOrder() {
      common_vendor.index.navigateTo({
        url: "/pages/login/login",
        success: () => {
          common_vendor.index.__f__("log", "at pages/home/<USER>", "跳转到登录页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/home/<USER>", "跳转到登录页面失败:", err);
          common_vendor.index.showToast({
            title: "跳转失败",
            icon: "error"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goToWarehouse && $options.goToWarehouse(...args)),
    b: common_vendor.o((...args) => $options.goToOrder && $options.goToOrder(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-07e72d3c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/home/<USER>
