
.login-container.data-v-e4e4508d {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 40rpx 60rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
}

	/* 头部样式 */
.header.data-v-e4e4508d {
		text-align: center;
		margin-bottom: 80rpx;
}
.logo-section.data-v-e4e4508d {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
}
.logo-icon.data-v-e4e4508d {
		font-size: 80rpx;
		margin-right: 20rpx;
}
.logo-text.data-v-e4e4508d {
		font-size: 48rpx;
		font-weight: bold;
		color: white;
}
.subtitle.data-v-e4e4508d {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
}

	/* 表单样式 */
.form-container.data-v-e4e4508d {
		background: rgba(255, 255, 255, 0.95);
		border-radius: 20rpx;
		padding: 50rpx 40rpx;
		box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
		box-sizing: border-box;
		max-width: 100%;
		overflow: hidden;
		flex-shrink: 0;
		max-height: 70vh;
}
.form-item.data-v-e4e4508d {
		margin-bottom: 35rpx;
		width: 100%;
		box-sizing: border-box;
}
.input-label.data-v-e4e4508d {
		display: flex;
		align-items: center;
		margin-bottom: 15rpx;
}
.label-icon.data-v-e4e4508d {
		font-size: 32rpx;
		margin-right: 15rpx;
		color: #667eea;
}
.label-text.data-v-e4e4508d {
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
}
.form-input.data-v-e4e4508d {
		width: 100%;
		height: 90rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 12rpx;
		padding: 0 30rpx;
		font-size: 32rpx;
		color: #333333;
		background-color: #ffffff;
		transition: all 0.3s ease;
		box-sizing: border-box;
}

	/* 密码输入框容器 */
.password-input-container.data-v-e4e4508d {
		position: relative;
		width: 100%;
}
.password-input.data-v-e4e4508d {
		padding-right: 80rpx !important;
}

	/* 密码显示/隐藏按钮 */
.password-toggle.data-v-e4e4508d {
		position: absolute;
		right: 20rpx;
		top: 50%;
		transform: translateY(-50%);
		width: 50rpx;
		height: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		z-index: 10;
}
.toggle-icon.data-v-e4e4508d {
		font-size: 32rpx;
		color: #999999;
		transition: color 0.3s ease;
}
.toggle-icon.active.data-v-e4e4508d {
		color: #667eea;
}
.password-toggle:active .toggle-icon.data-v-e4e4508d {
		color: #667eea;
}
.form-input.data-v-e4e4508d:focus {
		border-color: #667eea;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}
.form-input.data-v-e4e4508d:disabled {
		background-color: #f5f5f5;
		color: #999999;
}
.login-btn.data-v-e4e4508d {
		width: 100%;
		height: 90rpx;
		background: linear-gradient(135deg, #667eea, #764ba2);
		border: none;
		border-radius: 12rpx;
		color: #ffffff;
		font-size: 36rpx;
		font-weight: bold;
		margin-top: 40rpx;
		transition: all 0.3s ease;
}
.login-btn.data-v-e4e4508d:not(:disabled):active {
		transform: scale(0.98);
}
.login-btn.data-v-e4e4508d:disabled {
		opacity: 0.6;
		transform: none;
}
.login-btn.loading.data-v-e4e4508d {
		background: #cccccc;
}
.loading-text.data-v-e4e4508d {
		color: #666666;
}

	/* 底部 */
.footer.data-v-e4e4508d {
		text-align: center;
		margin-top: auto;
}
.footer-text.data-v-e4e4508d {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.7);
}
.loading-overlay.data-v-e4e4508d {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
}
.loading-content.data-v-e4e4508d {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 60rpx 40rpx;
		text-align: center;
		min-width: 300rpx;
}
.loading-spinner.data-v-e4e4508d {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #667eea;
		border-radius: 50%;
		animation: spin-e4e4508d 1s linear infinite;
		margin: 0 auto 30rpx;
}
.loading-message.data-v-e4e4508d {
		font-size: 28rpx;
		color: #666666;
}
@keyframes spin-e4e4508d {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}

	/* 响应式设计 */
@media screen and (max-width: 750rpx) {
.login-container.data-v-e4e4508d {
			padding: 40rpx 20rpx;
}
.logo-text.data-v-e4e4508d {
			font-size: 42rpx;
}
.form-container.data-v-e4e4508d {
			padding: 50rpx 30rpx;
			margin: 0 10rpx;
}
.form-input.data-v-e4e4508d {
			padding: 0 25rpx;
}
.password-input.data-v-e4e4508d {
			padding-right: 70rpx !important;
}
.password-toggle.data-v-e4e4508d {
			right: 15rpx;
			width: 45rpx;
			height: 45rpx;
}
.toggle-icon.data-v-e4e4508d {
			font-size: 28rpx;
}
}
