# 本地开发环境配置
NODE_ENV=development
PORT=3001
HOST=localhost

# 域名配置
DOMAIN=localhost
API_BASE_URL=http://localhost:3001

# 数据库配置（使用远程数据库）
DB_HOST=************
DB_PORT=3306
DB_USER=mls01
DB_PASSWORD=12345@Mls
DB_NAME=identify

# 本地文件存储路径
WAREHOUSE_IMG_DIR=./uploads/warehouse
ORDER_IMG_DIR=./uploads/order
TEMP_UPLOAD_DIR=./uploads/temp

# 日志配置
LOG_LEVEL=debug
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# 安全配置
JWT_SECRET=mls2005_jwt_secret_key_2024
SESSION_SECRET=mls2005_session_secret_key_2024

# 性能配置
MAX_UPLOAD_SIZE=50mb
REQUEST_TIMEOUT=30000
DB_CONNECTION_LIMIT=10
DB_ACQUIRE_TIMEOUT=30000

# 监控配置
ENABLE_MONITORING=true
HEALTH_CHECK_INTERVAL=30000
