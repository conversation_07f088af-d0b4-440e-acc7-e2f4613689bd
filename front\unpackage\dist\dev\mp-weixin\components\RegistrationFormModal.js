"use strict";
const common_vendor = require("../common/vendor.js");
const utils_helpers = require("../utils/helpers.js");
const utils_apiService = require("../utils/apiService.js");
const _sfc_main = {
  name: "RegistrationFormModal",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default: () => []
    },
    imageInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      registrationData: []
    };
  },
  watch: {
    data: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.registrationData = newVal.map((row) => ({
            ...row,
            Date: utils_helpers.formatDateForDisplay(row.Date)
          }));
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    /**
     * 编辑数据
     */
    editData(index, field, value) {
      if (index >= 0 && index < this.registrationData.length) {
        this.registrationData[index][field] = value;
      }
    },
    /**
     * 确认保存
     */
    async confirm() {
      try {
        utils_helpers.showLoading("正在保存数据...");
        await utils_apiService.apiService.saveRegistrationFormData(this.registrationData, this.imageInfo);
        utils_helpers.hideLoading();
        utils_helpers.showSuccess("保存成功！");
        this.$emit("confirm", this.registrationData);
      } catch (error) {
        utils_helpers.hideLoading();
        common_vendor.index.__f__("error", "at components/RegistrationFormModal.vue:158", "保存装柜放船样登记表数据失败:", error);
        utils_helpers.showError(error.message || "保存数据时发生错误");
      }
    },
    /**
     * 取消
     */
    cancel() {
      this.$emit("cancel");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.visible
  }, $props.visible ? {
    b: common_vendor.t($data.registrationData.length),
    c: common_vendor.f($data.registrationData, (row, index, i0) => {
      return {
        a: common_vendor.o([($event) => row.Date = $event.detail.value, ($event) => $options.editData(index, "Date", $event.detail.value)], index),
        b: row.Date,
        c: common_vendor.o([($event) => row.OrderNumber = $event.detail.value, ($event) => $options.editData(index, "OrderNumber", $event.detail.value)], index),
        d: row.OrderNumber,
        e: common_vendor.o([($event) => row.CategoryName = $event.detail.value, ($event) => $options.editData(index, "CategoryName", $event.detail.value)], index),
        f: row.CategoryName,
        g: common_vendor.o([($event) => row.SalesmanName = $event.detail.value, ($event) => $options.editData(index, "SalesmanName", $event.detail.value)], index),
        h: row.SalesmanName,
        i: common_vendor.o([($event) => row.IsPlace = $event.detail.value, ($event) => $options.editData(index, "IsPlace", $event.detail.value)], index),
        j: row.IsPlace,
        k: common_vendor.o([($event) => row.Comment = $event.detail.value, ($event) => $options.editData(index, "Comment", $event.detail.value)], index),
        l: row.Comment,
        m: index
      };
    }),
    d: common_vendor.o((...args) => $options.cancel && $options.cancel(...args)),
    e: common_vendor.o((...args) => $options.confirm && $options.confirm(...args))
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4d23352c"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/RegistrationFormModal.js.map
