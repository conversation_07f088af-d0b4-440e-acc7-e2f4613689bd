
.image-picker.data-v-8ce556f9 {
		margin-bottom: 40rpx;
}

	/* 多图片预览区域 */
.images-preview.data-v-8ce556f9 {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 40rpx;
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}
.images-grid.data-v-8ce556f9 {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		margin-bottom: 20rpx;
}
.image-item.data-v-8ce556f9 {
		position: relative;
		width: 200rpx;
		height: 200rpx;
		border-radius: 15rpx;
		overflow: hidden;
}
.preview-image.data-v-8ce556f9 {
		width: 100%;
		height: 100%;
		border-radius: 15rpx;
}
.image-remove.data-v-8ce556f9 {
		position: absolute;
		top: 10rpx;
		right: 10rpx;
		width: 40rpx;
		height: 40rpx;
		background: rgba(255, 0, 0, 0.8);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
}
.remove-icon.data-v-8ce556f9 {
		color: white;
		font-size: 24rpx;
		font-weight: bold;
}
.image-index.data-v-8ce556f9 {
		position: absolute;
		bottom: 10rpx;
		left: 10rpx;
		background: rgba(0, 0, 0, 0.6);
		color: white;
		padding: 5rpx 10rpx;
		border-radius: 10rpx;
		font-size: 20rpx;
}
.add-more-btn.data-v-8ce556f9 {
		width: 200rpx;
		height: 200rpx;
		border: 2rpx dashed #cccccc;
		border-radius: 15rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: #fafafa;
}
.add-icon.data-v-8ce556f9 {
		font-size: 60rpx;
		color: #999999;
		margin-bottom: 10rpx;
}
.add-text.data-v-8ce556f9 {
		font-size: 24rpx;
		color: #999999;
}
.images-info.data-v-8ce556f9 {
		text-align: center;
}
.info-text.data-v-8ce556f9 {
		font-size: 28rpx;
		color: #666666;
		display: block;
		margin-bottom: 10rpx;
}
.limit-text.data-v-8ce556f9 {
		font-size: 24rpx;
		color: #999999;
		display: block;
}
.file-name.data-v-8ce556f9 {
		font-size: 24rpx;
		color: #999999;
		display: block;
}

	/* 空状态 */
.empty-state.data-v-8ce556f9 {
		background: rgba(255, 255, 255, 0.9);
		border-radius: 20rpx;
		padding: 80rpx 30rpx;
		text-align: center;
		margin-bottom: 40rpx;
		border: 2rpx dashed #cccccc;
}
.empty-icon.data-v-8ce556f9 {
		font-size: 120rpx;
		margin-bottom: 30rpx;
		opacity: 0.6;
		display: block;
}
.empty-text.data-v-8ce556f9 {
		font-size: 32rpx;
		color: #999999;
		display: block;
		margin-bottom: 10rpx;
}

	/* 操作按钮 */
.action-buttons.data-v-8ce556f9 {
		display: flex;
		justify-content: space-between;
		gap: 30rpx;
}
.btn.data-v-8ce556f9 {
		flex: 1;
		background: rgba(255, 255, 255, 0.9);
		border: none;
		border-radius: 15rpx;
		padding: 40rpx 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
		transition: all 0.3s ease;
}
.btn.data-v-8ce556f9:active {
		transform: translateY(2rpx);
		box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.2);
}
.btn-icon.data-v-8ce556f9 {
		font-size: 60rpx;
		margin-bottom: 15rpx;
		display: block;
}
.btn-text.data-v-8ce556f9 {
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
}
.btn-camera.data-v-8ce556f9 {
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}
.btn-album.data-v-8ce556f9 {
		background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}
