<template>
	<view class="order-container">
		<!-- 头部信息 -->
		<view class="header">
			<view class="factory-info">
				<text class="factory-icon">🏭</text>
				<view class="factory-details">
					<text class="factory-name">{{ userInfo.factory_name || '未知工厂' }}</text>
					<text class="welcome-text">欢迎，{{ userInfo.username }}</text>
				</view>
			</view>
			<view class="logout-btn" @click="handleLogout">
				<text class="logout-icon">🚪</text>
			</view>
		</view>

		<!-- 订单查询区域 -->
		<view class="new-order-section">
			<view class="section-title">
				<text class="title-icon">🔍</text>
				<text class="title-text">订单查询</text>
			</view>
			<view class="input-group">
				<input
					class="order-input"
					type="text"
					v-model="newOrderNumber"
					placeholder="请输入订单号进行查询"
					:disabled="isLoading"
					@confirm="handleQueryOrder"
				/>
				<button
					class="query-btn"
					:disabled="!newOrderNumber.trim() || isLoading"
					@click="handleQueryOrder"
				>
					<text class="btn-text">查询</text>
				</button>
			</view>
			
		</view>

		<!-- 查询结果区域 -->
		<view class="query-result-section" v-if="showQueryResult">
			<view class="section-title">
				<text class="title-icon">🔍</text>
				<text class="title-text">查询结果</text>
				<view class="close-query" @click="closeQueryResult">
					<text class="close-icon">✕</text>
				</view>
			</view>

			<!-- 查询加载状态 -->
			<view class="loading-section" v-if="isQuerying">
				<view class="loading-spinner"></view>
				<text class="loading-text">查询中...</text>
			</view>

			<!-- 查询结果 -->
			<view class="query-content" v-else>
				<!-- 查询成功 -->
				<view class="query-success" v-if="queryResult">
					<!-- 单个结果 -->
					<view v-if="!Array.isArray(queryResult)" class="single-result">
						<view class="order-item query-order-item" @click="goToImageManage(queryResult.order_number)">
							<view class="order-info">
								<view class="order-number">
									<text class="order-label">订单号：</text>
									<text class="order-value">{{ queryResult.order_number }}</text>
								</view>
								<view class="order-time">
									<text class="time-label">创建时间：</text>
									<text class="time-value">{{ formatTime(queryResult.created_date) }}</text>
								</view>
								<view class="order-time">
									<text class="time-label">最新上传：</text>
									<text class="time-value">{{ formatTime(queryResult.latest_upload) }}</text>
								</view>
								<view class="order-images">
									<text class="images-label">图片数量：</text>
									<text class="images-value">{{ queryResult.image_count || 0 }} 张</text>
								</view>
							</view>
							<view class="order-arrow">
								<text class="arrow">→</text>
							</view>
						</view>
					</view>

					<!-- 多个结果 -->
					<view v-else class="multiple-results">
						<view class="result-header">
							<text class="result-count">找到 {{ queryResult.length }} 个匹配的订单</text>
						</view>
						<scroll-view class="query-list" scroll-y="true" :style="{ maxHeight: '400rpx' }">
							<view
								class="order-item query-order-item"
								v-for="(order, index) in queryResult"
								:key="index"
								@click="goToImageManage(order.order_number)"
							>
								<view class="order-info">
									<view class="order-number">
										<text class="order-label">订单号：</text>
										<text class="order-value">{{ order.order_number }}</text>
									</view>
									<view class="order-time">
										<text class="time-label">创建时间：</text>
										<text class="time-value">{{ formatTime(order.created_date) }}</text>
									</view>
									<view class="order-time">
										<text class="time-label">最新上传：</text>
										<text class="time-value">{{ formatTime(order.latest_upload) }}</text>
									</view>
									<view class="order-images">
										<text class="images-label">图片数量：</text>
										<text class="images-value">{{ order.image_count || 0 }} 张</text>
									</view>
								</view>
								<view class="order-arrow">
									<text class="arrow">→</text>
								</view>
							</view>
						</scroll-view>
					</view>
				</view>

				<!-- 查询失败 -->
				<view class="query-empty" v-else>
					<text class="empty-icon">🔍</text>
					<text class="empty-text">未找到匹配的订单</text>
					<text class="empty-hint">请尝试输入订单号的部分内容进行模糊查询</text>
				</view>
			</view>
		</view>

		<!-- 历史订单列表 -->
		<view class="history-section">
			<view class="section-title">
				<text class="title-icon">📋</text>
				<text class="title-text">历史订单</text>
				<text class="order-count">({{ orderList.length }})</text>
			</view>

			<!-- 加载状态 -->
			<view class="loading-section" v-if="isLoadingOrders">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载订单列表中...</text>
			</view>

			<!-- 订单列表 -->
			<scroll-view
				class="order-list"
				scroll-y="true"
				v-else-if="orderList.length > 0"
				refresher-enabled="true"
				:refresher-triggered="isRefreshing"
				@refresherrefresh="handleRefresh"
				:scroll-with-animation="true"
				:enable-back-to-top="true"
			>
				<view
					class="order-item"
					v-for="(order, index) in orderList"
					:key="index"
					@click="goToImageManage(order.order_number)"
				>
					<view class="order-info">
						<view class="order-number">
							<text class="order-label">订单号：</text>
							<text class="order-value">{{ order.order_number }}</text>
						</view>
						<view class="order-time">
							<text class="time-label">创建时间：</text>
							<text class="time-value">{{ formatTime(order.created_date) }}</text>
						</view>
						<view class="order-time">
							<text class="time-label">最新上传：</text>
							<text class="time-value">{{ formatTime(order.latest_upload) }}</text>
						</view>
						<view class="order-images">
							<text class="images-label">图片数量：</text>
							<text class="images-value">{{ order.image_count || 0 }} 张</text>
						</view>
					</view>
					<view class="order-arrow">
						<text class="arrow">→</text>
					</view>
				</view>
			</scroll-view>

			<!-- 空状态 -->
			<view class="empty-state" v-else>
				<text class="empty-icon">📦</text>
				<text class="empty-text">暂无订单记录</text>
				<text class="empty-hint">请先新增订单</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { showError } from '../../utils/helpers.js';
	import userManager from '../../utils/userManager.js';
	import urlConfig from '../../utils/urlConfig.js';

	export default {
		data() {
			return {
				userInfo: {},
				newOrderNumber: '',
				orderList: [],
				isLoading: false,
				isLoadingOrders: false,
				isRefreshing: false,
				// 查询相关
				showQueryResult: false,
				isQuerying: false,
				queryResult: null
			}
		},
		methods: {


			/**
			 * 处理查询订单（基于shipping_detail表，支持模糊查询）
			 */
			async handleQueryOrder() {
				if (!this.newOrderNumber.trim()) {
					showError('请输入订单号或关键词');
					return;
				}

				this.isQuerying = true;
				this.showQueryResult = true;
				this.queryResult = null;

				try {
					const searchTerm = this.newOrderNumber.trim();
					console.log('开始模糊查询订单:', searchTerm);
					const response = await this.queryOrderRequest(searchTerm);

					if (response.success && response.data) {
						this.queryResult = response.data;

						// 显示查询结果信息
						if (Array.isArray(response.data)) {
							console.log(`模糊查询成功: 找到 ${response.data.length} 个匹配的订单`);
							uni.showToast({
								title: `找到 ${response.data.length} 个匹配订单`,
								icon: 'success',
								duration: 2000
							});
						} else {
							console.log('精确查询成功:', this.queryResult);
							uni.showToast({
								title: '查询成功',
								icon: 'success',
								duration: 1500
							});
						}
					} else {
						this.queryResult = null;
						console.log('未找到匹配的订单:', searchTerm);
						uni.showToast({
							title: '未找到匹配的订单',
							icon: 'none',
							duration: 2000
						});
					}
				} catch (error) {
					console.error('查询订单失败:', error);
					this.queryResult = null;
					showError('查询失败: ' + error.message);
				} finally {
					this.isQuerying = false;
				}
			},

			/**
			 * 关闭查询结果
			 */
			closeQueryResult() {
				this.showQueryResult = false;
				this.queryResult = null;
				this.newOrderNumber = '';
			},

			/**
			 * 跳转到图片管理页面
			 */
			goToImageManage(orderNumber) {
				uni.navigateTo({
					url: `/pages/imageManage/imageManage?orderNumber=${encodeURIComponent(orderNumber)}`,
					success: () => {
						console.log('跳转到图片管理页面:', orderNumber);
					},
					fail: (err) => {
						console.error('跳转失败:', err);
						showError('页面跳转失败');
					}
				});
			},

			/**
			 * 加载历史订单
			 */
			async loadOrderHistory() {
				if (!this.userInfo.factory_name) {
					console.log('用户工厂名称为空，跳过加载订单历史');
					return;
				}

				this.isLoadingOrders = true;

				try {
					console.log('开始加载订单历史...');
					const response = await this.getOrderHistoryRequest();

					if (response.success) {
						this.orderList = response.data || [];
						console.log('订单历史加载成功:', this.orderList.length, '个订单');
					} else {
						console.error('获取订单历史失败:', response.message);
						this.orderList = [];
					}
				} catch (error) {
					console.error('加载订单历史失败:', error);
					this.orderList = [];
					showError('加载订单历史失败');
				} finally {
					this.isLoadingOrders = false;
				}
			},

			/**
			 * 获取订单历史请求
			 */
			async getOrderHistoryRequest() {
				return new Promise((resolve, reject) => {
					const requestConfig = userManager.createAuthRequest({
						url: urlConfig.getApiUrl('/api/orders/history'),
						method: 'GET',
						timeout: 10000,
						success: (res) => {
							if (res.statusCode === 200) {
								resolve(res.data);
							} else {
								reject(new Error(`服务器错误 (${res.statusCode})`));
							}
						},
						fail: (err) => {
							console.error('获取订单历史请求失败:', err);
							reject(new Error('网络连接失败'));
						}
					});

					uni.request(requestConfig);
				});
			},

			/**
			 * 查询订单请求
			 */
			async queryOrderRequest(orderNumber) {
				return new Promise((resolve, reject) => {
					const requestConfig = userManager.createAuthRequest({
						url: urlConfig.getApiUrl(`/api/orders/query/${encodeURIComponent(orderNumber)}`),
						method: 'GET',
						timeout: 10000,
						success: (res) => {
							if (res.statusCode === 200) {
								resolve(res.data);
							} else if (res.statusCode === 404) {
								// 订单不存在
								resolve({ success: false, message: '订单不存在' });
							} else {
								reject(new Error(`服务器错误 (${res.statusCode})`));
							}
						},
						fail: (err) => {
							console.error('查询订单请求失败:', err);
							reject(new Error('网络连接失败'));
						}
					});

					uni.request(requestConfig);
				});
			},

			/**
			 * 下拉刷新
			 */
			async handleRefresh() {
				console.log('开始下拉刷新...');
				this.isRefreshing = true;
				try {
					await this.loadOrderHistory();
				} catch (error) {
					console.error('下拉刷新失败:', error);
				} finally {
					this.isRefreshing = false;
					console.log('下拉刷新完成');
				}
			},

			/**
			 * 处理退出登录
			 */
			handleLogout() {
				uni.showModal({
					title: '确认退出',
					content: '确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							// 使用用户管理器退出登录
							userManager.logout();

							// 跳转到主页面
							uni.reLaunch({
								url: '/pages/home/<USER>'
							});
						}
					}
				});
			},

			/**
			 * 格式化时间
			 */
			formatTime(timeStr) {
				if (!timeStr) return '未知';
				
				try {
					const date = new Date(timeStr);
					const now = new Date();
					const diff = now - date;
					
					if (diff < 60000) { // 1分钟内
						return '刚刚';
					} else if (diff < 3600000) { // 1小时内
						return `${Math.floor(diff / 60000)}分钟前`;
					} else if (diff < 86400000) { // 1天内
						return `${Math.floor(diff / 3600000)}小时前`;
					} else {
						return date.toLocaleDateString();
					}
				} catch (error) {
					return '未知';
				}
			},

			/**
			 * 初始化数据
			 */
			initData() {
				// 确保数据结构正确
				this.orderList = [];
				this.isLoadingOrders = false;
				this.isRefreshing = false;
				this.isLoading = false;
				this.newOrderNumber = '';
			},

			/**
			 * 初始化用户信息
			 */
			initUserInfo() {
				// 初始化数据
				this.initData();

				// 使用用户管理器检查登录状态
				if (userManager.requireLogin()) {
					this.userInfo = userManager.getUserInfo();
					// 延迟加载订单历史，确保页面完全渲染
					this.$nextTick(() => {
						setTimeout(() => {
							this.loadOrderHistory();
						}, 100);
					});
				}
				// 如果未登录，requireLogin会自动跳转到登录页面
			}
		},

		onLoad() {
			this.initUserInfo();
		},

		onShow() {
			// 页面显示时刷新订单列表
			this.$nextTick(() => {
				if (this.userInfo.factory_name) {
					this.loadOrderHistory();
				}
			});
		},

		onReady() {
			// 页面初次渲染完成
			console.log('订单页面渲染完成');
		}
	}
</script>

<style scoped>
	.order-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 20rpx;
	}

	.header {
		background: linear-gradient(135deg, #667eea, #764ba2);
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
	}

	.factory-info {
		display: flex;
		align-items: center;
		flex: 1;
	}

	.factory-icon {
		font-size: 50rpx;
		margin-right: 20rpx;
	}

	.factory-details {
		display: flex;
		flex-direction: column;
	}

	.factory-name {
		font-size: 36rpx;
		font-weight: bold;
		color: #ffffff;
		margin-bottom: 8rpx;
	}

	.welcome-text {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.logout-btn {
		width: 80rpx;
		height: 80rpx;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.logout-icon {
		font-size: 36rpx;
		color: #ffffff;
	}

	.new-order-section {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.section-title {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.title-icon {
		font-size: 36rpx;
		margin-right: 15rpx;
		color: #667eea;
	}

	.title-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.order-count {
		font-size: 24rpx;
		color: #999999;
		margin-left: 10rpx;
	}

	.input-group {
		display: flex;
		gap: 20rpx;
	}

	.order-input {
		flex: 1;
		height: 80rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 12rpx;
		padding: 0 25rpx;
		font-size: 30rpx;
		color: #333333;
		background-color: #ffffff;
	}

	.order-input:focus {
		border-color: #667eea;
	}

	.add-btn {
		width: 120rpx;
		height: 80rpx;
		background: linear-gradient(135deg, #667eea, #764ba2);
		border: none;
		border-radius: 12rpx;
		color: #ffffff;
		font-size: 28rpx;
		font-weight: bold;
	}

	.add-btn:disabled {
		background: #cccccc;
	}

	.query-btn {
		width: 120rpx;
		height: 80rpx;
		background: linear-gradient(135deg, #28a745, #20c997);
		border: none;
		border-radius: 12rpx;
		color: #ffffff;
		font-size: 28rpx;
		font-weight: bold;
	}

	.query-btn:disabled {
		background: #cccccc;
	}

	.query-tip {
		margin-top: 20rpx;
		padding: 15rpx 20rpx;
		background: #f8f9fa;
		border-radius: 10rpx;
		border-left: 4rpx solid #667eea;
	}

	.tip-text {
		font-size: 24rpx;
		color: #6c757d;
		line-height: 1.4;
	}

	/* 查询结果区域 */
	.query-result-section {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
		border: 2rpx solid #28a745;
	}

	.query-result-section .section-title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 30rpx;
	}

	.close-query {
		width: 60rpx;
		height: 60rpx;
		background: #f8f9fa;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1rpx solid #dee2e6;
	}

	.close-icon {
		font-size: 24rpx;
		color: #6c757d;
		font-weight: bold;
	}

	.query-content {
		min-height: 100rpx;
	}

	.query-order-item {
		border: 2rpx solid #28a745;
		background: #f8fff9;
	}

	.query-success {
		animation: fadeIn 0.3s ease-in;
	}

	.single-result {
		margin-bottom: 20rpx;
	}

	.multiple-results {
		margin-bottom: 20rpx;
	}

	.result-header {
		padding: 20rpx 0;
		border-bottom: 1rpx solid #e0e0e0;
		margin-bottom: 20rpx;
	}

	.result-count {
		font-size: 28rpx;
		color: #28a745;
		font-weight: bold;
	}

	.query-list {
		max-height: 400rpx;
	}

	.query-list .order-item {
		margin-bottom: 20rpx;
	}

	.query-list .order-item:last-child {
		margin-bottom: 0;
	}

	.query-empty {
		text-align: center;
		padding: 60rpx 20rpx;
		color: #999999;
	}

	.query-empty .empty-icon {
		font-size: 80rpx;
		display: block;
		margin-bottom: 20rpx;
	}

	.query-empty .empty-text {
		font-size: 32rpx;
		color: #666666;
		display: block;
		margin-bottom: 10rpx;
	}

	.query-empty .empty-hint {
		font-size: 24rpx;
		color: #999999;
		display: block;
		line-height: 1.5;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(20rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.history-section {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		flex: 1;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.loading-section {
		text-align: center;
		padding: 60rpx 0;
	}

	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #667eea;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin: 0 auto 20rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #999999;
	}

	.order-list {
		max-height: 600rpx;
	}

	.order-item {
		display: flex;
		align-items: flex-start;
		padding: 30rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
		transition: background-color 0.3s ease;
		min-height: 120rpx;
	}

	.order-item:last-child {
		border-bottom: none;
	}

	.order-item:active {
		background-color: #f8f9ff;
	}

	.order-info {
		flex: 1;
	}

	.order-number {
		display: flex;
		align-items: center;
		margin-bottom: 15rpx;
	}

	.order-label {
		font-size: 28rpx;
		color: #666666;
		margin-right: 10rpx;
	}

	.order-value {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.order-time {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.order-time:last-child {
		margin-bottom: 0;
	}

	.time-label {
		font-size: 24rpx;
		color: #999999;
		margin-right: 10rpx;
	}

	.time-value {
		font-size: 24rpx;
		color: #999999;
	}

	.order-images {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.images-label {
		font-size: 24rpx;
		color: #999999;
		margin-right: 10rpx;
	}

	.images-value {
		font-size: 24rpx;
		color: #007aff;
		font-weight: bold;
	}

	.order-arrow {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 20rpx;
	}

	.arrow {
		font-size: 36rpx;
		color: #667eea;
		font-weight: bold;
	}

	.empty-state {
		text-align: center;
		padding: 80rpx 0;
	}

	.empty-icon {
		font-size: 80rpx;
		margin-bottom: 30rpx;
		display: block;
	}

	.empty-text {
		font-size: 32rpx;
		color: #666666;
		margin-bottom: 15rpx;
		display: block;
	}

	.empty-hint {
		font-size: 26rpx;
		color: #999999;
		display: block;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
</style>
