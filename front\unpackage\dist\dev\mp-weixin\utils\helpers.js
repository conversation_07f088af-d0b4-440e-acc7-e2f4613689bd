"use strict";
const common_vendor = require("../common/vendor.js");
function formatDateForDisplay(dateStr) {
  if (!dateStr)
    return "";
  const simplePattern = /^(\d+)\.(\d+)$/;
  const match = dateStr.match(simplePattern);
  if (match) {
    const month = match[1].padStart(2, "0");
    const day = match[2].padStart(2, "0");
    const currentYear = (/* @__PURE__ */ new Date()).getFullYear();
    return `${currentYear}-${month}-${day}`;
  }
  return dateStr;
}
function showSuccess(message, duration = 2e3) {
  common_vendor.index.showToast({
    title: message,
    icon: "success",
    duration
  });
}
function showError(message, duration = 3e3) {
  common_vendor.index.showToast({
    title: message,
    icon: "none",
    duration
  });
}
function showLoading(message = "加载中...") {
  common_vendor.index.showLoading({
    title: message
  });
}
function hideLoading() {
  common_vendor.index.hideLoading();
}
function showConfirm(title, content) {
  return new Promise((resolve) => {
    common_vendor.index.showModal({
      title,
      content,
      success: (res) => {
        resolve(res.confirm);
      },
      fail: () => {
        resolve(false);
      }
    });
  });
}
function deepClone(obj) {
  if (obj === null || typeof obj !== "object")
    return obj;
  if (obj instanceof Date)
    return new Date(obj.getTime());
  if (obj instanceof Array)
    return obj.map((item) => deepClone(item));
  if (typeof obj === "object") {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
}
exports.deepClone = deepClone;
exports.formatDateForDisplay = formatDateForDisplay;
exports.hideLoading = hideLoading;
exports.showConfirm = showConfirm;
exports.showError = showError;
exports.showLoading = showLoading;
exports.showSuccess = showSuccess;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/helpers.js.map
