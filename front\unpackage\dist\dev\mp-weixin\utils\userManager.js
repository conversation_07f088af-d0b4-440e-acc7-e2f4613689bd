"use strict";
const common_vendor = require("../common/vendor.js");
const USER_STORAGE_KEY = "userInfo";
const TOKEN_EXPIRY_KEY = "tokenExpiry";
class UserManager {
  constructor() {
    this.userInfo = null;
    this.isLoggedIn = false;
    this.init();
  }
  /**
   * 初始化用户状态
   */
  init() {
    try {
      const userInfo = common_vendor.index.getStorageSync(USER_STORAGE_KEY);
      const tokenExpiry = common_vendor.index.getStorageSync(TOKEN_EXPIRY_KEY);
      if (userInfo && userInfo.token && tokenExpiry) {
        const now = (/* @__PURE__ */ new Date()).getTime();
        const expiryTime = new Date(tokenExpiry).getTime();
        if (now < expiryTime) {
          this.userInfo = userInfo;
          this.isLoggedIn = true;
          this.updateGlobalState();
          common_vendor.index.__f__("log", "at utils/userManager.js:39", "✅ 用户状态恢复成功:", userInfo.username);
        } else {
          this.clearUserState();
          common_vendor.index.__f__("log", "at utils/userManager.js:43", "⚠️ Token已过期，已清除用户状态");
        }
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/userManager.js:47", "❌ 初始化用户状态失败:", error);
      this.clearUserState();
    }
  }
  /**
   * 用户登录
   * @param {Object} loginData 登录返回的数据
   */
  login(loginData) {
    try {
      const { username, factory_name, token, expires_in = 7200 } = loginData;
      const expiryTime = new Date(Date.now() + expires_in * 1e3);
      const userInfo = {
        username,
        factory_name,
        token,
        loginTime: (/* @__PURE__ */ new Date()).toISOString()
      };
      common_vendor.index.setStorageSync(USER_STORAGE_KEY, userInfo);
      common_vendor.index.setStorageSync(TOKEN_EXPIRY_KEY, expiryTime.toISOString());
      this.userInfo = userInfo;
      this.isLoggedIn = true;
      this.updateGlobalState();
      common_vendor.index.__f__("log", "at utils/userManager.js:81", "✅ 用户登录状态保存成功:", username);
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/userManager.js:85", "❌ 保存用户登录状态失败:", error);
      return false;
    }
  }
  /**
   * 用户退出登录
   */
  logout() {
    try {
      common_vendor.index.removeStorageSync(USER_STORAGE_KEY);
      common_vendor.index.removeStorageSync(TOKEN_EXPIRY_KEY);
      this.clearUserState();
      common_vendor.index.__f__("log", "at utils/userManager.js:102", "✅ 用户退出登录成功");
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/userManager.js:106", "❌ 用户退出登录失败:", error);
      return false;
    }
  }
  /**
   * 检查用户是否已登录
   */
  checkLoginStatus() {
    if (!this.isLoggedIn || !this.userInfo || !this.userInfo.token) {
      return false;
    }
    try {
      const tokenExpiry = common_vendor.index.getStorageSync(TOKEN_EXPIRY_KEY);
      if (tokenExpiry) {
        const now = (/* @__PURE__ */ new Date()).getTime();
        const expiryTime = new Date(tokenExpiry).getTime();
        if (now >= expiryTime) {
          this.clearUserState();
          return false;
        }
      }
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/userManager.js:135", "❌ 检查登录状态失败:", error);
      this.clearUserState();
      return false;
    }
  }
  /**
   * 获取用户信息
   */
  getUserInfo() {
    if (this.checkLoginStatus()) {
      return this.userInfo;
    }
    return null;
  }
  /**
   * 获取认证token
   */
  getToken() {
    if (this.checkLoginStatus()) {
      return this.userInfo.token;
    }
    return null;
  }
  /**
   * 获取工厂名称
   */
  getFactoryName() {
    if (this.checkLoginStatus()) {
      return this.userInfo.factory_name;
    }
    return null;
  }
  /**
   * 获取用户名
   */
  getUsername() {
    if (this.checkLoginStatus()) {
      return this.userInfo.username;
    }
    return null;
  }
  /**
   * 刷新token（延长有效期）
   * @param {number} expiresIn 新的过期时间（秒）
   */
  refreshToken(expiresIn = 7200) {
    if (this.checkLoginStatus()) {
      try {
        const expiryTime = new Date(Date.now() + expiresIn * 1e3);
        common_vendor.index.setStorageSync(TOKEN_EXPIRY_KEY, expiryTime.toISOString());
        common_vendor.index.__f__("log", "at utils/userManager.js:191", "✅ Token刷新成功");
        return true;
      } catch (error) {
        common_vendor.index.__f__("error", "at utils/userManager.js:194", "❌ Token刷新失败:", error);
        return false;
      }
    }
    return false;
  }
  /**
   * 清除用户状态
   */
  clearUserState() {
    this.userInfo = null;
    this.isLoggedIn = false;
    if (getApp && getApp().globalData) {
      getApp().globalData.userInfo = null;
    }
  }
  /**
   * 更新全局状态
   */
  updateGlobalState() {
    if (getApp && getApp().globalData) {
      getApp().globalData.userInfo = this.userInfo;
    }
  }
  /**
   * 自动跳转到登录页面
   */
  redirectToLogin() {
    common_vendor.index.redirectTo({
      url: "/pages/login/login",
      fail: (err) => {
        common_vendor.index.__f__("error", "at utils/userManager.js:230", "跳转到登录页面失败:", err);
        common_vendor.index.reLaunch({
          url: "/pages/login/login"
        });
      }
    });
  }
  /**
   * 检查登录状态并自动跳转
   * @param {boolean} autoRedirect 是否自动跳转到登录页面
   */
  requireLogin(autoRedirect = true) {
    const isLoggedIn = this.checkLoginStatus();
    if (!isLoggedIn && autoRedirect) {
      this.redirectToLogin();
    }
    return isLoggedIn;
  }
  /**
   * 获取认证请求头
   */
  getAuthHeaders() {
    const token = this.getToken();
    if (token) {
      return {
        "Authorization": `Bearer ${token}`,
        "Content-Type": "application/json"
      };
    }
    return {
      "Content-Type": "application/json"
    };
  }
  /**
   * 创建认证请求配置
   * @param {Object} config 请求配置
   */
  createAuthRequest(config = {}) {
    const token = this.getToken();
    return {
      ...config,
      header: {
        ...config.header,
        ...token ? { "Authorization": `Bearer ${token}` } : {}
      }
    };
  }
}
const userManager = new UserManager();
exports.userManager = userManager;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/userManager.js.map
