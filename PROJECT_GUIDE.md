# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个工厂订单图片管理系统，采用 uni-app + Express.js + MySQL 的全栈架构。系统支持微信小程序和H5平台，主要用于工厂用户上传和管理订单相关图片，具备OCR文字识别功能。

**技术栈：**
- 前端：uni-app (Vue 3) + 微信小程序 + H5
- 后端：Express.js + Node.js
- 数据库：MySQL (连接池配置)
- 部署：Nginx反向代理 + HTTPS + SSL
- 服务器：************ (域名: www.mls2005.top)

## 开发命令

### 前端开发 (front/)
```bash
# H5开发环境 (本地开发)
npm run dev:h5

# H5生产构建
npm run build:h5

# 微信小程序开发
npm run dev:mp-weixin

# 微信小程序构建
npm run build:mp-weixin

# 启动开发服务器
npm run serve
```

### 后端开发 (server/)
```bash
# 生产环境启动
npm start

# 开发环境 (使用nodemon)
npm run dev

# 运行测试 (当前为占位符)
npm test

# 安装依赖
npm install
```

### 部署命令
```bash
# 启动Node.js服务 (使用PM2)
pm2 start app.js --name "warehouse-api"

# 停止/重启服务
pm2 stop warehouse-api
pm2 restart warehouse-api

# Nginx配置测试和启动
nginx -t
nginx -s reload
nginx -s stop
```

## 核心架构

### 前端架构 (uni-app)
```
front/
├── pages/                      # 页面模块
│   ├── home/                   # 系统首页
│   ├── login/                  # 工厂用户登录
│   ├── warehouseLogin/         # 仓库用户登录  
│   ├── order/                  # 订单管理
│   ├── imageManage/            # 图片管理
│   ├── camera/                 # 拍照上传
│   └── index/                  # 仓库管理
├── components/                 # 公共组件
│   ├── VirtualImageGrid.vue    # 虚拟滚动图片网格
│   ├── LazyImage.vue          # 懒加载图片组件
│   ├── ImagePicker.vue        # 图片选择器
│   └── HistoryModal.vue       # 历史记录弹窗
├── utils/                      # 工具模块
│   ├── apiService.js          # API服务封装 (自动环境切换)
│   ├── userManager.js         # 用户认证管理
│   ├── imageCache.js          # 图片缓存系统
│   └── helpers.js            # 工具函数
```

### 后端架构 (Express.js)
```
server/
├── config/
│   └── database.js            # MySQL连接池配置 (100并发连接)
├── routes/                    # 模块化路由
│   ├── auth.js               # 用户认证API
│   ├── orders.js             # 订单管理API
│   ├── images.js             # 图片管理API  
│   ├── upload.js             # 文件上传API
│   └── recognize.js          # OCR识别API
├── middleware/               # 中间件
│   ├── logger.js            # 请求/错误日志
│   ├── errorHandler.js      # 全局错误处理
│   └── dbMonitor.js         # 数据库监控
├── utils/                   # 后端工具
│   ├── ocrServiceManager.js # OCR服务管理
│   ├── fileStorageManager.js # 文件存储管理
│   └── ocrTaskQueue.js      # OCR任务队列
```

### 数据库设计
**核心表结构：**
- `Factory_Login`: 工厂用户认证表
- `Img_Info`: 图片信息表 (订单图片管理)
- `UploadImage`: 上传图片记录表 (OCR识别)
- `cartoninfo`: 装柜信息表
- `registrationform`: 装柜放船样登记表

**连接池配置 (database.js):**
- 最大连接数: 100
- 连接超时: 60秒
- 队列限制: 200个请求
- 空闲超时: 5分钟
- 自动重连和监控

## 关键特性

### 1. 双重认证系统
- **工厂用户**: Token-based认证 (2小时有效期)
- **仓库用户**: 独立认证系统
- 认证Token存储在 `uni.storage` 中

### 2. 文件存储策略
```
C:\MLS\Order_Img\               # 订单图片存储
└── [工厂名]/
    └── [年-月]/
        └── [日]/
            └── [订单号]/
                └── 时间戳_随机数.扩展名

C:\MLS\Warehouse_Img\           # 仓库图片存储
C:\Warehouse_Img\temp_uploads\  # 临时上传目录
```

### 3. API环境自适应
**apiService.js 自动检测环境：**
- 微信小程序: `https://www.mls2005.top`
- H5 HTTPS: `https://www.mls2005.top`
- H5 本地开发: `http://localhost:3000`

### 4. 性能优化
- **前端**: 虚拟滚动、图片懒加载、分页加载、图片缓存
- **后端**: 连接池监控、查询优化、慢查询警告、异步文件操作

### 5. OCR文字识别集成
- 百度OCR API集成
- 支持装柜信息表格识别
- 手写表格识别
- 纸箱编号识别
- 装柜放船样登记表识别

## 环境配置

### 生产环境
- **域名**: https://www.mls2005.top  
- **IP访问**: https://************
- **API端点**: /api/*
- **静态资源**: /warehouse-images/, /order-images/

### 开发环境
- **前端**: http://localhost:3000 (H5开发)
- **后端**: http://localhost:3000 (Express服务)
- **数据库**: ************:3306

### SSL证书配置
```
server/ssl/
├── www.mls2005.top.pem
└── www.mls2005.top.key
```

## 重要提醒

### 开发注意事项
1. **认证要求**: 所有受保护的API都需要在请求头中包含 `Authorization: Bearer <token>`
2. **文件上传限制**: 最大文件大小 50MB，支持常见图片格式
3. **数据库监控**: 生产环境下会记录慢查询 (>1秒) 和连接池状态
4. **CORS配置**: 已配置跨域支持，包括微信小程序域名白名单

### 常用调试端点
- `GET /api/health` - 系统健康检查
- `GET /api/db-monitor` - 数据库连接池监控
- `POST /api/auth/login` - 用户登录测试
- `GET /api/orders/history` - 获取订单历史

### 日志系统
- **访问日志**: logs/access-YYYY-MM-DD.log
- **错误日志**: logs/error-YYYY-MM-DD.log  
- **应用日志**: logs/application-YYYY-MM-DD.log
- **性能日志**: logs/performance-YYYY-MM-DD.log

### 微信小程序配置
需要在微信小程序后台配置合法域名：
- **request合法域名**: `https://www.mls2005.top`
- **uploadFile合法域名**: `https://www.mls2005.top`
- **downloadFile合法域名**: `https://www.mls2005.top`