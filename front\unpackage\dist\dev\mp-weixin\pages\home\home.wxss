
.container.data-v-07e72d3c {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 40rpx 30rpx;
		display: flex;
		flex-direction: column;
}
.header.data-v-07e72d3c {
		text-align: center;
		margin-bottom: 80rpx;
		margin-top: 60rpx;
}
.title.data-v-07e72d3c {
		font-size: 48rpx;
		font-weight: bold;
		color: #ffffff;
		margin-bottom: 20rpx;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
.subtitle.data-v-07e72d3c {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
}
.function-area.data-v-07e72d3c {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 30rpx;
}
.function-card.data-v-07e72d3c {
		background: rgba(255, 255, 255, 0.95);
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
		transition: all 0.3s ease;
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
}
.function-card.data-v-07e72d3c:active {
		transform: scale(0.98);
		box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.2);
}
.card-icon.data-v-07e72d3c {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		background: linear-gradient(135deg, #667eea, #764ba2);
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 30rpx;
}
.icon.data-v-07e72d3c {
		font-size: 50rpx;
}
.card-content.data-v-07e72d3c {
		flex: 1;
}
.card-title.data-v-07e72d3c {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
}
.card-desc.data-v-07e72d3c {
		font-size: 26rpx;
		color: #666666;
		line-height: 1.4;
}
.card-arrow.data-v-07e72d3c {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.arrow.data-v-07e72d3c {
		font-size: 40rpx;
		color: #667eea;
		font-weight: bold;
}
.footer.data-v-07e72d3c {
		text-align: center;
		margin-top: 60rpx;
		padding-top: 40rpx;
}
.footer-text.data-v-07e72d3c {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.6);
}

	/* 响应式设计 */
@media screen and (max-width: 750rpx) {
.container.data-v-07e72d3c {
			padding: 30rpx 20rpx;
}
.title.data-v-07e72d3c {
			font-size: 42rpx;
}
.function-card.data-v-07e72d3c {
			padding: 35rpx 25rpx;
}
.card-icon.data-v-07e72d3c {
			width: 80rpx;
			height: 80rpx;
			margin-right: 25rpx;
}
.icon.data-v-07e72d3c {
			font-size: 40rpx;
}
.card-title.data-v-07e72d3c {
			font-size: 32rpx;
}
.card-desc.data-v-07e72d3c {
			font-size: 24rpx;
}
}
