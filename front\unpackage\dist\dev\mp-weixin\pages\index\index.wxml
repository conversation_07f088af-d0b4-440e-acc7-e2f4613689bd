<view class="container data-v-1cf27b2a"><view wx:if="{{a}}" class="header data-v-1cf27b2a"><view class="title-section data-v-1cf27b2a"><text class="title data-v-1cf27b2a">仓库管理</text><text class="subtitle data-v-1cf27b2a">图片文字识别系统</text></view><view class="user-section data-v-1cf27b2a"><view class="user-info data-v-1cf27b2a"><text class="user-name data-v-1cf27b2a">{{b}}</text><text class="user-dept data-v-1cf27b2a">{{c}}</text></view><button class="logout-btn data-v-1cf27b2a" bindtap="{{d}}"><text class="logout-icon data-v-1cf27b2a">🚪</text></button></view></view><image-picker wx:if="{{g}}" class="data-v-1cf27b2a" bindchange="{{e}}" u-i="1cf27b2a-0" bind:__l="__l" bindupdateModelValue="{{f}}" u-p="{{g}}"/><view wx:if="{{h}}" class="recognition-status data-v-1cf27b2a"><view class="status-content data-v-1cf27b2a"><view class="loading-spinner data-v-1cf27b2a"></view><text class="status-text data-v-1cf27b2a">{{i}}</text><view wx:if="{{j}}" class="progress-info data-v-1cf27b2a"><text class="progress-text data-v-1cf27b2a">{{k}}/{{l}}</text><view class="progress-bar data-v-1cf27b2a"><view class="progress-fill data-v-1cf27b2a" style="{{'width:' + m}}"></view></view></view></view></view><view wx:if="{{n}}" class="loading-overlay data-v-1cf27b2a"><view class="loading-content data-v-1cf27b2a"><view class="loading-spinner data-v-1cf27b2a"></view><text class="loading-text data-v-1cf27b2a">正在识别图片内容...</text></view></view><view class="history-section data-v-1cf27b2a"><button class="history-btn data-v-1cf27b2a" bindtap="{{o}}"><text class="btn-icon data-v-1cf27b2a">📋</text><text class="btn-text data-v-1cf27b2a">今日历史</text></button></view><history-modal wx:if="{{q}}" class="data-v-1cf27b2a" bindclose="{{p}}" u-i="1cf27b2a-1" bind:__l="__l" u-p="{{q}}"/><registration-form-modal wx:if="{{t}}" class="data-v-1cf27b2a" bindconfirm="{{r}}" bindcancel="{{s}}" u-i="1cf27b2a-2" bind:__l="__l" u-p="{{t}}"/><carton-info-modal wx:if="{{x}}" class="data-v-1cf27b2a" bindconfirm="{{v}}" bindcancel="{{w}}" u-i="1cf27b2a-3" bind:__l="__l" u-p="{{x}}"/></view>