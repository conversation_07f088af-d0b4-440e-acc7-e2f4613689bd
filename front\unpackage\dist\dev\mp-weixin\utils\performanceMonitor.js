"use strict";
const common_vendor = require("../common/vendor.js");
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      renderTime: [],
      loadTime: [],
      memoryUsage: [],
      networkRequests: [],
      cacheHitRate: {
        image: { hits: 0, total: 0 },
        data: { hits: 0, total: 0 }
      }
    };
    this.timers = /* @__PURE__ */ new Map();
    this.enabled = true;
    this.maxMetrics = 100;
  }
  /**
   * 开始计时
   * @param {string} name 计时器名称
   */
  startTimer(name) {
    if (!this.enabled)
      return;
    this.timers.set(name, {
      startTime: Date.now(),
      startMemory: this.getCurrentMemoryUsage()
    });
  }
  /**
   * 结束计时并记录
   * @param {string} name 计时器名称
   * @param {string} category 指标类别
   * @returns {number} 耗时（毫秒）
   */
  endTimer(name, category = "general") {
    if (!this.enabled)
      return 0;
    const timer = this.timers.get(name);
    if (!timer) {
      common_vendor.index.__f__("warn", "at utils/performanceMonitor.js:54", `计时器 ${name} 不存在`);
      return 0;
    }
    const endTime = Date.now();
    const duration = endTime - timer.startTime;
    const endMemory = this.getCurrentMemoryUsage();
    const memoryDelta = endMemory - timer.startMemory;
    this.recordMetric(category, {
      name,
      duration,
      memoryDelta,
      timestamp: endTime
    });
    this.timers.delete(name);
    return duration;
  }
  /**
   * 记录指标
   * @param {string} category 指标类别
   * @param {Object} metric 指标数据
   */
  recordMetric(category, metric) {
    if (!this.enabled)
      return;
    if (!this.metrics[category]) {
      this.metrics[category] = [];
    }
    this.metrics[category].push(metric);
    if (this.metrics[category].length > this.maxMetrics) {
      this.metrics[category].shift();
    }
  }
  /**
   * 记录渲染性能
   * @param {string} component 组件名称
   * @param {number} itemCount 渲染项目数量
   * @param {number} duration 渲染耗时
   */
  recordRenderPerformance(component, itemCount, duration) {
    this.recordMetric("renderTime", {
      component,
      itemCount,
      duration,
      timestamp: Date.now()
    });
  }
  /**
   * 记录加载性能
   * @param {string} type 加载类型
   * @param {number} size 数据大小
   * @param {number} duration 加载耗时
   */
  recordLoadPerformance(type, size, duration) {
    this.recordMetric("loadTime", {
      type,
      size,
      duration,
      timestamp: Date.now()
    });
  }
  /**
   * 记录网络请求
   * @param {string} url 请求URL
   * @param {string} method 请求方法
   * @param {number} duration 请求耗时
   * @param {number} size 响应大小
   * @param {boolean} fromCache 是否来自缓存
   */
  recordNetworkRequest(url, method, duration, size, fromCache = false) {
    this.recordMetric("networkRequests", {
      url,
      method,
      duration,
      size,
      fromCache,
      timestamp: Date.now()
    });
  }
  /**
   * 记录缓存命中率
   * @param {string} type 缓存类型 (image/data)
   * @param {boolean} hit 是否命中
   */
  recordCacheHit(type, hit) {
    if (!this.metrics.cacheHitRate[type]) {
      this.metrics.cacheHitRate[type] = { hits: 0, total: 0 };
    }
    this.metrics.cacheHitRate[type].total++;
    if (hit) {
      this.metrics.cacheHitRate[type].hits++;
    }
  }
  /**
   * 获取当前内存使用情况
   * @returns {number} 内存使用量（MB）
   */
  getCurrentMemoryUsage() {
    if (typeof performance !== "undefined" && performance.memory) {
      return performance.memory.usedJSHeapSize / 1024 / 1024;
    }
    return 0;
  }
  /**
   * 获取性能统计信息
   * @returns {Object} 性能统计
   */
  getPerformanceStats() {
    const stats = {
      renderTime: this.calculateStats(this.metrics.renderTime, "duration"),
      loadTime: this.calculateStats(this.metrics.loadTime, "duration"),
      networkRequests: this.calculateNetworkStats(),
      cacheHitRate: this.calculateCacheHitRate(),
      memoryUsage: this.getCurrentMemoryUsage()
    };
    return stats;
  }
  /**
   * 计算统计信息
   * @param {Array} data 数据数组
   * @param {string} field 统计字段
   * @returns {Object} 统计结果
   */
  calculateStats(data, field) {
    if (!data || data.length === 0) {
      return { count: 0, avg: 0, min: 0, max: 0 };
    }
    const values = data.map((item) => item[field]).filter((v) => typeof v === "number");
    if (values.length === 0) {
      return { count: 0, avg: 0, min: 0, max: 0 };
    }
    const sum = values.reduce((a, b) => a + b, 0);
    const avg = sum / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);
    return {
      count: values.length,
      avg: Math.round(avg * 100) / 100,
      min,
      max
    };
  }
  /**
   * 计算网络请求统计
   * @returns {Object} 网络请求统计
   */
  calculateNetworkStats() {
    const requests = this.metrics.networkRequests || [];
    const total = requests.length;
    const fromCache = requests.filter((r) => r.fromCache).length;
    const avgDuration = requests.length > 0 ? requests.reduce((sum, r) => sum + r.duration, 0) / requests.length : 0;
    return {
      total,
      fromCache,
      cacheRate: total > 0 ? Math.round(fromCache / total * 100) : 0,
      avgDuration: Math.round(avgDuration * 100) / 100
    };
  }
  /**
   * 计算缓存命中率
   * @returns {Object} 缓存命中率统计
   */
  calculateCacheHitRate() {
    const result = {};
    for (const [type, data] of Object.entries(this.metrics.cacheHitRate)) {
      result[type] = {
        hits: data.hits,
        total: data.total,
        rate: data.total > 0 ? Math.round(data.hits / data.total * 100) : 0
      };
    }
    return result;
  }
  /**
   * 生成性能报告
   * @returns {string} 性能报告
   */
  generateReport() {
    const stats = this.getPerformanceStats();
    let report = "=== 性能监控报告 ===\n\n";
    report += `渲染性能:
`;
    report += `  平均耗时: ${stats.renderTime.avg}ms
`;
    report += `  最小耗时: ${stats.renderTime.min}ms
`;
    report += `  最大耗时: ${stats.renderTime.max}ms
`;
    report += `  渲染次数: ${stats.renderTime.count}

`;
    report += `加载性能:
`;
    report += `  平均耗时: ${stats.loadTime.avg}ms
`;
    report += `  最小耗时: ${stats.loadTime.min}ms
`;
    report += `  最大耗时: ${stats.loadTime.max}ms
`;
    report += `  加载次数: ${stats.loadTime.count}

`;
    report += `网络请求:
`;
    report += `  总请求数: ${stats.networkRequests.total}
`;
    report += `  缓存命中: ${stats.networkRequests.fromCache}
`;
    report += `  缓存命中率: ${stats.networkRequests.cacheRate}%
`;
    report += `  平均耗时: ${stats.networkRequests.avgDuration}ms

`;
    report += `缓存命中率:
`;
    for (const [type, data] of Object.entries(stats.cacheHitRate)) {
      report += `  ${type}: ${data.rate}% (${data.hits}/${data.total})
`;
    }
    report += `
当前内存使用: ${stats.memoryUsage.toFixed(2)}MB
`;
    return report;
  }
  /**
   * 清除所有指标
   */
  clearMetrics() {
    this.metrics = {
      renderTime: [],
      loadTime: [],
      memoryUsage: [],
      networkRequests: [],
      cacheHitRate: {
        image: { hits: 0, total: 0 },
        data: { hits: 0, total: 0 }
      }
    };
    this.timers.clear();
  }
  /**
   * 启用/禁用监控
   * @param {boolean} enabled 是否启用
   */
  setEnabled(enabled) {
    this.enabled = enabled;
  }
}
const performanceMonitor = new PerformanceMonitor();
exports.performanceMonitor = performanceMonitor;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/performanceMonitor.js.map
