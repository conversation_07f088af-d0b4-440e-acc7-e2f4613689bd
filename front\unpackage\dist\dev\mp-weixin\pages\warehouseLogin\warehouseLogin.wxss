
.login-container.data-v-6c98fa9f {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 40rpx 60rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
}

	/* 头部样式 */
.header.data-v-6c98fa9f {
		text-align: center;
		margin-bottom: 80rpx;
}
.logo-section.data-v-6c98fa9f {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
}
.logo-icon.data-v-6c98fa9f {
		font-size: 80rpx;
		margin-right: 20rpx;
}
.logo-title.data-v-6c98fa9f {
		font-size: 48rpx;
		font-weight: bold;
		color: white;
}
.subtitle.data-v-6c98fa9f {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
}

	/* 表单样式 */
.form-container.data-v-6c98fa9f {
		background: rgba(255, 255, 255, 0.95);
		border-radius: 20rpx;
		padding: 50rpx 40rpx;
		box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
		box-sizing: border-box;
		max-width: 100%;
		overflow: hidden;
		flex-shrink: 0;
		max-height: 70vh;
}
.form-item.data-v-6c98fa9f {
		margin-bottom: 35rpx;
		width: 100%;
		box-sizing: border-box;
}
.input-label.data-v-6c98fa9f {
		display: flex;
		align-items: center;
		margin-bottom: 15rpx;
}
.label-icon.data-v-6c98fa9f {
		font-size: 32rpx;
		margin-right: 15rpx;
		color: #667eea;
}
.label-text.data-v-6c98fa9f {
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
}
.form-input.data-v-6c98fa9f {
		width: 100%;
		height: 90rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 12rpx;
		padding: 0 30rpx;
		font-size: 32rpx;
		color: #333333;
		background-color: #ffffff;
		transition: all 0.3s ease;
		box-sizing: border-box;
}

	/* 密码输入框容器 */
.password-input-container.data-v-6c98fa9f {
		position: relative;
		width: 100%;
}
.password-input.data-v-6c98fa9f {
		padding-right: 80rpx !important;
}
.form-input.data-v-6c98fa9f:focus {
		border-color: #667eea;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}
.form-input.data-v-6c98fa9f:disabled {
		background-color: #f5f5f5;
		color: #999999;
}

	/* 密码显示/隐藏按钮 */
.password-toggle.data-v-6c98fa9f {
		position: absolute;
		right: 20rpx;
		top: 50%;
		transform: translateY(-50%);
		width: 50rpx;
		height: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		z-index: 10;
}
.toggle-icon.data-v-6c98fa9f {
		font-size: 32rpx;
		color: #999999;
		transition: color 0.3s ease;
}
.toggle-icon.active.data-v-6c98fa9f {
		color: #667eea;
}
.password-toggle:active .toggle-icon.data-v-6c98fa9f {
		color: #667eea;
}

	/* 登录按钮 */
.login-btn.data-v-6c98fa9f {
		width: 100%;
		height: 90rpx;
		background: linear-gradient(135deg, #667eea, #764ba2);
		border: none;
		border-radius: 12rpx;
		color: #ffffff;
		font-size: 36rpx;
		font-weight: bold;
		margin-top: 40rpx;
		transition: all 0.3s ease;
}
.login-btn.data-v-6c98fa9f:not(:disabled):active {
		transform: scale(0.98);
}
.login-btn.data-v-6c98fa9f:disabled {
		opacity: 0.6;
		transform: none;
}
.login-btn.loading.data-v-6c98fa9f {
		background: #cccccc;
}
.loading-text.data-v-6c98fa9f {
		color: #666666;
}





	/* 响应式设计 */
@media screen and (max-width: 750rpx) {
.login-container.data-v-6c98fa9f {
			padding: 40rpx 20rpx;
}
.logo-title.data-v-6c98fa9f {
			font-size: 42rpx;
}
.form-container.data-v-6c98fa9f {
			padding: 50rpx 30rpx;
			margin: 0 10rpx;
}
.form-input.data-v-6c98fa9f {
			padding: 0 25rpx;
}
.password-input.data-v-6c98fa9f {
			padding-right: 70rpx !important;
}
.password-toggle.data-v-6c98fa9f {
			right: 15rpx;
			width: 45rpx;
			height: 45rpx;
}
.toggle-icon.data-v-6c98fa9f {
			font-size: 28rpx;
}
}

	/* 底部 */
.footer.data-v-6c98fa9f {
		text-align: center;
		margin-top: auto;
}
.footer-text.data-v-6c98fa9f {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.7);
}
