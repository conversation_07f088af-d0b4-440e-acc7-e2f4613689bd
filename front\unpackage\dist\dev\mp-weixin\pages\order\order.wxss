
.order-container.data-v-93207a4f {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 20rpx;
}
.header.data-v-93207a4f {
		background: linear-gradient(135deg, #667eea, #764ba2);
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}
.factory-info.data-v-93207a4f {
		display: flex;
		align-items: center;
		flex: 1;
}
.factory-icon.data-v-93207a4f {
		font-size: 50rpx;
		margin-right: 20rpx;
}
.factory-details.data-v-93207a4f {
		display: flex;
		flex-direction: column;
}
.factory-name.data-v-93207a4f {
		font-size: 36rpx;
		font-weight: bold;
		color: #ffffff;
		margin-bottom: 8rpx;
}
.welcome-text.data-v-93207a4f {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.8);
}
.logout-btn.data-v-93207a4f {
		width: 80rpx;
		height: 80rpx;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
}
.logout-icon.data-v-93207a4f {
		font-size: 36rpx;
		color: #ffffff;
}
.new-order-section.data-v-93207a4f {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.section-title.data-v-93207a4f {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
}
.title-icon.data-v-93207a4f {
		font-size: 36rpx;
		margin-right: 15rpx;
		color: #667eea;
}
.title-text.data-v-93207a4f {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
}
.order-count.data-v-93207a4f {
		font-size: 24rpx;
		color: #999999;
		margin-left: 10rpx;
}
.input-group.data-v-93207a4f {
		display: flex;
		gap: 20rpx;
}
.order-input.data-v-93207a4f {
		flex: 1;
		height: 80rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 12rpx;
		padding: 0 25rpx;
		font-size: 30rpx;
		color: #333333;
		background-color: #ffffff;
}
.order-input.data-v-93207a4f:focus {
		border-color: #667eea;
}
.add-btn.data-v-93207a4f {
		width: 120rpx;
		height: 80rpx;
		background: linear-gradient(135deg, #667eea, #764ba2);
		border: none;
		border-radius: 12rpx;
		color: #ffffff;
		font-size: 28rpx;
		font-weight: bold;
}
.add-btn.data-v-93207a4f:disabled {
		background: #cccccc;
}
.query-btn.data-v-93207a4f {
		width: 120rpx;
		height: 80rpx;
		background: linear-gradient(135deg, #28a745, #20c997);
		border: none;
		border-radius: 12rpx;
		color: #ffffff;
		font-size: 28rpx;
		font-weight: bold;
}
.query-btn.data-v-93207a4f:disabled {
		background: #cccccc;
}

	/* 查询结果区域 */
.query-result-section.data-v-93207a4f {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
		border: 2rpx solid #28a745;
}
.query-result-section .section-title.data-v-93207a4f {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 30rpx;
}
.close-query.data-v-93207a4f {
		width: 60rpx;
		height: 60rpx;
		background: #f8f9fa;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1rpx solid #dee2e6;
}
.close-icon.data-v-93207a4f {
		font-size: 24rpx;
		color: #6c757d;
		font-weight: bold;
}
.query-content.data-v-93207a4f {
		min-height: 100rpx;
}
.query-order-item.data-v-93207a4f {
		border: 2rpx solid #28a745;
		background: #f8fff9;
}
.query-success.data-v-93207a4f {
		animation: fadeIn-93207a4f 0.3s ease-in;
}
.single-result.data-v-93207a4f {
		margin-bottom: 20rpx;
}
.multiple-results.data-v-93207a4f {
		margin-bottom: 20rpx;
}
.result-header.data-v-93207a4f {
		padding: 20rpx 0;
		border-bottom: 1rpx solid #e0e0e0;
		margin-bottom: 20rpx;
}
.result-count.data-v-93207a4f {
		font-size: 28rpx;
		color: #28a745;
		font-weight: bold;
}
.query-list.data-v-93207a4f {
		max-height: 400rpx;
}
.query-list .order-item.data-v-93207a4f {
		margin-bottom: 20rpx;
}
.query-list .order-item.data-v-93207a4f:last-child {
		margin-bottom: 0;
}
.query-empty.data-v-93207a4f {
		text-align: center;
		padding: 60rpx 20rpx;
		color: #999999;
}
.query-empty .empty-icon.data-v-93207a4f {
		font-size: 80rpx;
		display: block;
		margin-bottom: 20rpx;
}
.query-empty .empty-text.data-v-93207a4f {
		font-size: 32rpx;
		color: #666666;
		display: block;
		margin-bottom: 10rpx;
}
.query-empty .empty-hint.data-v-93207a4f {
		font-size: 24rpx;
		color: #999999;
		display: block;
		line-height: 1.5;
}
@keyframes fadeIn-93207a4f {
from {
			opacity: 0;
			transform: translateY(20rpx);
}
to {
			opacity: 1;
			transform: translateY(0);
}
}
.history-section.data-v-93207a4f {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		flex: 1;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.loading-section.data-v-93207a4f {
		text-align: center;
		padding: 60rpx 0;
}
.loading-spinner.data-v-93207a4f {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #667eea;
		border-radius: 50%;
		animation: spin-93207a4f 1s linear infinite;
		margin: 0 auto 20rpx;
}
.loading-text.data-v-93207a4f {
		font-size: 28rpx;
		color: #999999;
}
.order-list.data-v-93207a4f {
		max-height: 600rpx;
}
.order-item.data-v-93207a4f {
		display: flex;
		align-items: flex-start;
		padding: 30rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
		transition: background-color 0.3s ease;
		min-height: 120rpx;
}
.order-item.data-v-93207a4f:last-child {
		border-bottom: none;
}
.order-item.data-v-93207a4f:active {
		background-color: #f8f9ff;
}
.order-info.data-v-93207a4f {
		flex: 1;
}
.order-number.data-v-93207a4f {
		display: flex;
		align-items: center;
		margin-bottom: 15rpx;
}
.order-label.data-v-93207a4f {
		font-size: 28rpx;
		color: #666666;
		margin-right: 10rpx;
}
.order-value.data-v-93207a4f {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
}
.order-time.data-v-93207a4f {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
}
.order-time.data-v-93207a4f:last-child {
		margin-bottom: 0;
}
.time-label.data-v-93207a4f {
		font-size: 24rpx;
		color: #999999;
		margin-right: 10rpx;
}
.time-value.data-v-93207a4f {
		font-size: 24rpx;
		color: #999999;
}
.order-images.data-v-93207a4f {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
}
.images-label.data-v-93207a4f {
		font-size: 24rpx;
		color: #999999;
		margin-right: 10rpx;
}
.images-value.data-v-93207a4f {
		font-size: 24rpx;
		color: #007aff;
		font-weight: bold;
}
.order-arrow.data-v-93207a4f {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 20rpx;
}
.arrow.data-v-93207a4f {
		font-size: 36rpx;
		color: #667eea;
		font-weight: bold;
}
.empty-state.data-v-93207a4f {
		text-align: center;
		padding: 80rpx 0;
}
.empty-icon.data-v-93207a4f {
		font-size: 80rpx;
		margin-bottom: 30rpx;
		display: block;
}
.empty-text.data-v-93207a4f {
		font-size: 32rpx;
		color: #666666;
		margin-bottom: 15rpx;
		display: block;
}
.empty-hint.data-v-93207a4f {
		font-size: 26rpx;
		color: #999999;
		display: block;
}
@keyframes spin-93207a4f {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
