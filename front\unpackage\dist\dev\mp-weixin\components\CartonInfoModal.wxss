
	/* 装柜信息表确认弹窗样式 */
.carton-modal-overlay.data-v-acfbbe68 {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
}
.carton-modal.data-v-acfbbe68 {
		background: white;
		border-radius: 20rpx;
		width: 90%;
		max-width: 800rpx;
		height: 80vh;
		display: flex;
		flex-direction: column;
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}
.modal-header.data-v-acfbbe68 {
		padding: 30rpx 40rpx 20rpx;
		border-bottom: 2rpx solid #f0f0f0;
		text-align: center;
		flex-shrink: 0;
		background: white;
		z-index: 10;
}
.modal-title.data-v-acfbbe68 {
		font-size: 36rpx;
		font-weight: bold;
		color: #2c3e50;
		display: block;
		margin-bottom: 10rpx;
}
.modal-subtitle.data-v-acfbbe68 {
		font-size: 26rpx;
		color: #666;
		display: block;
}
.carton-modal-content.data-v-acfbbe68 {
		flex: 1;
		overflow-y: auto;
		padding: 30rpx;
		display: flex;
		flex-direction: column;
}
.carton-info-section.data-v-acfbbe68,
	.carton-totals-section.data-v-acfbbe68 {
		margin-bottom: 30rpx;
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 10rpx;
}
.carton-orders-section.data-v-acfbbe68 {
		margin-bottom: 30rpx;
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 10rpx;
}
.orders-table-container.data-v-acfbbe68 {
		border-radius: 8rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		width: 100%;
}

	/* 横向滚动容器 */
.orders-table-horizontal-scroll.data-v-acfbbe68 {
		width: 100%;
		white-space: nowrap;
}

	/* 表格包装器 - 设置最小宽度确保横向滚动 */
.orders-table-wrapper.data-v-acfbbe68 {
		display: inline-block;
		min-width: 1150rpx; /* 总列宽：250+300+200+300=1050rpx，加上边距和边框 */
		width: 100%;
}
.section-title.data-v-acfbbe68 {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		padding-bottom: 10rpx;
		border-bottom: 2rpx solid #e9ecef;
}
.info-row.data-v-acfbbe68 {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
}
.info-label.data-v-acfbbe68 {
		width: 120rpx;
		font-size: 28rpx;
		color: #666;
		font-weight: 500;
}
.info-input.data-v-acfbbe68 {
		flex: 1;
		padding: 15rpx 20rpx;
		border: 2rpx solid #e9ecef;
		border-radius: 8rpx;
		font-size: 28rpx;
		background: white;
}
.info-input.data-v-acfbbe68:focus {
		border-color: #667eea;
		outline: none;
}

	/* 订单表格样式 */
.orders-table-header.data-v-acfbbe68 {
		display: flex;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 8rpx 8rpx 0 0;
		overflow: hidden;
		min-width: 1150rpx; /* 与包装器宽度保持一致 */
}
.order-header-cell.data-v-acfbbe68 {
		padding: 20rpx 10rpx;
		text-align: center;
		font-size: 26rpx;
		font-weight: bold;
		color: white;
		border-right: 2rpx solid rgba(255, 255, 255, 0.2);
		white-space: nowrap; /* 防止文字换行 */
}
.order-header-cell.data-v-acfbbe68:last-child {
		border-right: none;
}

	/* 纵向滚动容器的包装器 */
.orders-table-scroll-container.data-v-acfbbe68 {
		border: 2rpx solid #e9ecef;
		border-top: none;
		border-radius: 0 0 8rpx 8rpx;
		background: white;
}
.orders-table-scroll.data-v-acfbbe68 {
		height: 600rpx; /* 固定高度，确保有足够空间显示多行数据 */
		background: white;
}
.orders-table-body.data-v-acfbbe68 {
		background: white;
}
.order-row.data-v-acfbbe68 {
		display: flex;
		border-bottom: 2rpx solid #e9ecef;
}
.order-row.data-v-acfbbe68:last-child {
		border-bottom: none;
}
.order-cell.data-v-acfbbe68 {
		padding: 10rpx;
		border-right: 2rpx solid #e9ecef;
		white-space: nowrap; /* 防止内容换行 */
}
.order-cell.data-v-acfbbe68:last-child {
		border-right: none;
}
.order-input.data-v-acfbbe68 {
		width: 100%;
		padding: 15rpx 10rpx;
		border: 1rpx solid #ddd;
		border-radius: 6rpx;
		font-size: 24rpx;
		text-align: center;
		background: white;
}
.order-input.data-v-acfbbe68:focus {
		border-color: #667eea;
		outline: none;
}

	/* 固定列宽定义 - 确保横向滚动时列宽一致 */
.order-col-fixed.data-v-acfbbe68 {
		width: 250rpx;
		min-width: 250rpx;
		max-width: 250rpx;
}
.category-col-fixed.data-v-acfbbe68 {
		width: 300rpx;
		min-width: 300rpx;
		max-width: 300rpx;
}
.piece-col-fixed.data-v-acfbbe68 {
		width: 200rpx;
		min-width: 200rpx;
		max-width: 200rpx;
}
.weight-col-fixed.data-v-acfbbe68 {
		width: 300rpx;
		min-width: 300rpx;
		max-width: 300rpx;
}

	/* 底部按钮区域 */
.modal-footer.data-v-acfbbe68 {
		padding: 30rpx 40rpx;
		border-top: 2rpx solid #f0f0f0;
		display: flex;
		gap: 30rpx;
		justify-content: center;
		flex-shrink: 0;
		background: white;
		box-shadow: 0 -5rpx 15rpx rgba(0, 0, 0, 0.1);
}
.cancel-btn.data-v-acfbbe68,
	.confirm-btn.data-v-acfbbe68 {
		flex: 1;
		max-width: 300rpx;
		padding: 30rpx 0;
		border-radius: 25rpx;
		font-size: 32rpx;
		font-weight: bold;
		border: none;
		cursor: pointer;
		transition: all 0.3s ease;
}
.cancel-btn.data-v-acfbbe68 {
		background: #f5f5f5;
		color: #666;
		border: 2rpx solid #ddd;
}
.cancel-btn.data-v-acfbbe68:hover {
		background: #e0e0e0;
		transform: translateY(-2rpx);
}
.confirm-btn.data-v-acfbbe68 {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
}
.confirm-btn.data-v-acfbbe68:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.4);
}
