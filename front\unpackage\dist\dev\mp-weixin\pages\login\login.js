"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_helpers = require("../../utils/helpers.js");
const utils_userManager = require("../../utils/userManager.js");
const utils_urlConfig = require("../../utils/urlConfig.js");
const _sfc_main = {
  data() {
    return {
      loginForm: {
        username: "",
        password: ""
      },
      isLoading: false,
      showPassword: false
    };
  },
  computed: {
    canSubmit() {
      return this.loginForm.username.trim() && this.loginForm.password.trim();
    },
    passwordInputType() {
      return this.showPassword ? "text" : "password";
    },
    passwordToggleIcon() {
      return this.showPassword ? "🙈" : "👁️";
    }
  },
  methods: {
    /**
     * 切换密码显示状态
     */
    togglePassword() {
      common_vendor.index.__f__("log", "at pages/login/login.vue:108", "切换密码显示状态，当前状态:", this.showPassword);
      this.showPassword = !this.showPassword;
      common_vendor.index.__f__("log", "at pages/login/login.vue:110", "切换后状态:", this.showPassword);
      this.$forceUpdate();
    },
    /**
     * 处理登录
     */
    async handleLogin() {
      if (!this.canSubmit) {
        utils_helpers.showError("请输入用户名和密码");
        return;
      }
      this.isLoading = true;
      try {
        const response = await this.loginRequest();
        if (response.success) {
          const loginSuccess = utils_userManager.userManager.login(response.data);
          if (loginSuccess) {
            utils_helpers.showSuccess("登录成功");
            setTimeout(() => {
              common_vendor.index.redirectTo({
                url: "/pages/order/order"
              });
            }, 1e3);
          } else {
            utils_helpers.showError("保存登录信息失败");
          }
        } else {
          utils_helpers.showError(response.message || "登录失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/login/login.vue:150", "登录失败:", error);
        utils_helpers.showError(error.message || "登录失败，请检查网络连接");
      } finally {
        this.isLoading = false;
      }
    },
    /**
     * 登录请求
     */
    async loginRequest() {
      return new Promise((resolve, reject) => {
        common_vendor.index.request({
          url: utils_urlConfig.urlConfig.getApiUrl("/api/auth/login"),
          method: "POST",
          data: {
            username: this.loginForm.username.trim(),
            password: this.loginForm.password.trim()
          },
          header: {
            "Content-Type": "application/json"
          },
          timeout: 1e4,
          success: (res) => {
            resolve(res.data);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/login/login.vue:179", "登录请求失败:", err);
            reject(new Error("网络连接失败"));
          }
        });
      });
    }
  },
  onLoad() {
    if (utils_userManager.userManager.checkLoginStatus()) {
      common_vendor.index.redirectTo({
        url: "/pages/order/order"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.isLoading,
    b: $data.loginForm.username,
    c: common_vendor.o(($event) => $data.loginForm.username = $event.detail.value),
    d: $options.passwordInputType,
    e: !$data.showPassword,
    f: $data.isLoading,
    g: common_vendor.o((...args) => $options.handleLogin && $options.handleLogin(...args)),
    h: $data.loginForm.password,
    i: common_vendor.o(($event) => $data.loginForm.password = $event.detail.value),
    j: common_vendor.t($options.passwordToggleIcon),
    k: $data.showPassword ? 1 : "",
    l: common_vendor.o((...args) => $options.togglePassword && $options.togglePassword(...args)),
    m: $data.isLoading
  }, $data.isLoading ? {} : {}, {
    n: $data.isLoading ? 1 : "",
    o: $data.isLoading || !$options.canSubmit,
    p: common_vendor.o((...args) => $options.handleLogin && $options.handleLogin(...args)),
    q: $data.isLoading
  }, $data.isLoading ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e4e4508d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map
