<template>
	<view class="image-manage-container">
		<!-- 头部信息 -->
		<view class="header">
			<view class="order-info">
				<text class="order-icon">📦</text>
				<view class="order-details">
					<text class="order-label">订单号</text>
					<text class="order-number">{{ orderNumber }}</text>
				</view>
			</view>
			<view class="image-count">
				<text class="count-text">{{ pagination.total || imageList.length }} 张图片</text>
			</view>
		</view>

		<!-- 发货单号选择 -->
		<view class="shipping-selector" v-if="shippingNumbers.length > 1">
			<view class="selector-title">
				<text class="title-icon">📦</text>
				<text class="title-text">发货单号</text>
			</view>
			<view class="shipping-tabs">
				<view
					class="shipping-tab"
					:class="{ active: currentShippingNumber === 'ALL' }"
					@click="switchShippingNumber('ALL')"
				>
					<text class="tab-text">全部</text>
				</view>
				<view
					v-for="(shippingNo, index) in shippingNumbers"
					:key="index"
					class="shipping-tab"
					:class="{ active: currentShippingNumber === shippingNo }"
					@click="switchShippingNumber(shippingNo)"
				>
					<text class="tab-text">{{ shippingNo }}</text>
				</view>
			</view>
		</view>

		<!-- 当前发货单号显示 -->
		<view class="current-shipping" v-if="shippingNumbers.length === 1">
			<text class="shipping-info">📦 发货单号: {{ shippingNumbers[0] }}</text>
		</view>

		<!-- 操作按钮区域 -->
		<view class="action-section">
			<button class="action-btn add-btn" @click="goToCamera">
				<text class="btn-icon">📷</text>
				<text class="btn-text">新增</text>
			</button>
			<button
				class="action-btn select-btn"
				:class="{ 'active': isSelectMode }"
				@click="toggleSelectMode"
			>
				<text class="btn-icon">{{ isSelectMode ? '✓' : '🗑️' }}</text>
				<text class="btn-text">{{ isSelectMode ? '取消选择' : '选择删除' }}</text>
			</button>
			<button
				class="action-btn delete-btn"
				:class="{ 'disabled': selectedImages.length === 0 }"
				:disabled="selectedImages.length === 0"
				@click="handleDeleteSelected"
				v-if="isSelectMode"
			>
				<text class="btn-icon">🗑️</text>
				<text class="btn-text">删除 ({{ selectedImages.length }})</text>
			</button>
		</view>

		<!-- 选择模式提示 -->
		<view class="select-tip" v-if="isSelectMode">
			<text class="tip-text">💡 点击图片进行选择，长按可以快速选择</text>
		</view>

		<!-- 图片网格区域 -->
		<view class="image-grid-section">
			<!-- 加载状态 -->
			<view class="loading-section" v-if="isLoading">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载图片中...</text>
			</view>

			<!-- 图片网格 -->
			<view v-else-if="hasImages" class="image-grid">
				<view
					class="image-item"
					v-for="(image, index) in imageList"
					:key="image.id"
					@click="handleImageClick({ image, index })"
					@longpress="handleImageLongPress(image)"
				>
					<!-- 选择状态覆盖层 -->
					<view class="select-overlay" v-if="isSelectMode">
						<view
							class="select-checkbox"
							:class="{ 'selected': selectedImages.includes(image.id) }"
						>
							<text class="checkbox-icon" v-if="selectedImages.includes(image.id)">✓</text>
						</view>
					</view>

					<!-- 图片 -->
					<image
						class="image"
						:src="getImageUrl(image.image_path, image.id)"
						mode="aspectFill"
						:lazy-load="true"
						@error="handleImageError"
					/>

					<!-- 图片信息 -->
					<view class="image-info">
						<text class="image-name">{{ image.image_name }}</text>
						<text class="image-time">{{ formatTime(image.upload_date) }}</text>
					</view>
				</view>
			</view>

			<!-- 分页控件 -->
			<view v-if="pagination.totalPages > 1" class="pagination-section">
				<view class="pagination-info">
					<text class="page-info">第 {{ pagination.page }} 页，共 {{ pagination.totalPages }} 页</text>
					<text class="total-info">总计 {{ pagination.total }} 张图片</text>
				</view>
				<view class="pagination-controls">
					<button
						class="page-btn prev-btn"
						:disabled="!pagination.hasPrev"
						@click="goToPrevPage"
					>
						上一页
					</button>
					<view class="page-numbers">
						<text
							class="page-number"
							:class="{ 'current': pagination.page === pageNum }"
							v-for="pageNum in getPageNumbers()"
							:key="pageNum"
							@click="goToPage(pageNum)"
						>
							{{ pageNum }}
						</text>
					</view>
					<button
						class="page-btn next-btn"
						:disabled="!pagination.hasNext"
						@click="goToNextPage"
					>
						下一页
					</button>
				</view>
			</view>

			<!-- 空状态 -->
			<view class="empty-state" v-else-if="shouldShowEmpty">
				<text class="empty-icon">📷</text>
				<text class="empty-text">暂无图片</text>
				<text class="empty-hint">点击"新增"按钮上传图片</text>
			</view>
		</view>

		<!-- 图片预览弹窗 -->
		<view class="preview-modal" v-if="showPreview" @click="closePreview">
			<view class="preview-content" @click.stop>
				<view class="preview-header">
					<text class="preview-title">图片预览</text>
					<text class="close-btn" @click="closePreview">✕</text>
				</view>
				<swiper 
					class="preview-swiper" 
					:current="currentPreviewIndex"
					@change="onSwiperChange"
				>
					<swiper-item v-for="image in imageList" :key="image.id">
						<image
							class="preview-image"
							:src="getImageUrl(image.image_path, image.id)"
							mode="aspectFit"
						/>
					</swiper-item>
				</swiper>
				<view class="preview-info">
					<text class="preview-name">{{ currentPreviewImage?.image_name }}</text>
					<text class="preview-index">{{ currentPreviewIndex + 1 }} / {{ imageList.length }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { showSuccess, showError, showConfirm } from '../../utils/helpers.js';
	import userManager from '../../utils/userManager.js';
	import dataCacheManager from '../../utils/dataCache.js';
	import performanceMonitor from '../../utils/performanceMonitor.js';
	import urlConfig from '../../utils/urlConfig.js';

	export default {
		data() {
			return {
				orderNumber: '',
				userInfo: {},
				imageList: [],
				selectedImages: [],
				isSelectMode: false,
				isLoading: false,
				isRefreshing: false,
				showPreview: false,
				currentPreviewIndex: 0,
				// 分页相关数据
				pagination: {
					page: 1,
					limit: 10,
					total: 0,
					totalPages: 0,
					hasNext: false,
					hasPrev: false
				},
				// 活动的计时器列表
				activeTimers: new Set(),
				// 发货单号相关
				shippingNumbers: [],
				currentShippingNumber: 'ALL'
			}
		},
		computed: {
			currentPreviewImage() {
				return this.imageList[this.currentPreviewIndex];
			},

			/**
			 * 是否有图片数据
			 */
			hasImages() {
				return this.imageList && Array.isArray(this.imageList) && this.imageList.length > 0;
			},

			/**
			 * 是否应该显示空状态
			 */
			shouldShowEmpty() {
				return !this.isLoading && !this.hasImages;
			}
		},
		methods: {
			/**
			 * 获取发货单号列表
			 */
			async loadShippingNumbers() {
				try {
					const requestConfig = userManager.createAuthRequest({
						url: urlConfig.getApiUrl(`/api/orders/${encodeURIComponent(this.orderNumber)}/shipping-numbers`),
						method: 'GET',
						timeout: 10000
					});

					const response = await uni.request(requestConfig);

					if (response.statusCode === 200 && response.data.success) {
						this.shippingNumbers = response.data.data;
						// 如果只有一个发货单号，直接设置为当前选中
						if (this.shippingNumbers.length === 1) {
							this.currentShippingNumber = this.shippingNumbers[0];
						}
						console.log('📦 获取发货单号列表成功:', this.shippingNumbers);
					} else {
						throw new Error(response.data?.message || '获取发货单号失败');
					}
				} catch (error) {
					console.error('❌ 获取发货单号列表失败:', error);
					// 如果获取失败，设置默认值
					this.shippingNumbers = ['DEFAULT'];
					this.currentShippingNumber = 'DEFAULT';
				}
			},

			/**
			 * 切换发货单号
			 */
			async switchShippingNumber(shippingNumber) {
				if (this.currentShippingNumber === shippingNumber) {
					return;
				}

				this.currentShippingNumber = shippingNumber;
				console.log('🔄 切换发货单号:', shippingNumber);

				// 重新加载图片列表
				this.pagination.page = 1;
				await this.loadImages(true);
			},

			/**
			 * 跳转到拍照上传页面
			 */
			goToCamera() {
				const currentShipping = this.currentShippingNumber === 'ALL'
					? (this.shippingNumbers[0] || 'DEFAULT')
					: this.currentShippingNumber;

				uni.navigateTo({
					url: `/pages/camera/camera?orderNumber=${encodeURIComponent(this.orderNumber)}&shippingNumber=${encodeURIComponent(currentShipping)}`,
					success: () => {
						console.log('跳转到拍照页面');
					},
					fail: (err) => {
						console.error('跳转失败:', err);
						showError('页面跳转失败');
					}
				});
			},

			/**
			 * 分页相关方法
			 */
			// 上一页
			goToPrevPage() {
				if (this.pagination.hasPrev) {
					this.pagination.page--;
					this.loadImages();
				}
			},

			// 下一页
			goToNextPage() {
				if (this.pagination.hasNext) {
					this.pagination.page++;
					this.loadImages();
				}
			},

			// 跳转到指定页
			goToPage(pageNum) {
				if (pageNum !== this.pagination.page && pageNum >= 1 && pageNum <= this.pagination.totalPages) {
					this.pagination.page = pageNum;
					this.loadImages();
				}
			},

			// 获取页码数组
			getPageNumbers() {
				const { page, totalPages } = this.pagination;
				const numbers = [];

				// 显示当前页前后各2页
				const start = Math.max(1, page - 2);
				const end = Math.min(totalPages, page + 2);

				for (let i = start; i <= end; i++) {
					numbers.push(i);
				}

				return numbers;
			},

			// 格式化时间
			formatTime(timeStr) {
				if (!timeStr) return '未知';
				try {
					const date = new Date(timeStr);
					return date.toLocaleString('zh-CN', {
						year: 'numeric',
						month: '2-digit',
						day: '2-digit',
						hour: '2-digit',
						minute: '2-digit'
					});
				} catch (error) {
					return '未知';
				}
			},

			// 获取图片URL
			getImageUrl(imagePath, imageId) {
				return urlConfig.getImageUrl(imagePath, imageId);
			},

			/**
			 * 处理图片点击
			 */
			handleImageClick(event) {
				const { image, index } = event;
				if (this.isSelectMode) {
					this.toggleImageSelection(image.id);
				} else {
					this.showImagePreview(index);
				}
			},

			/**
			 * 处理图片长按
			 */
			handleImageLongPress(image) {
				if (!this.isSelectMode) {
					this.isSelectMode = true;
					this.selectedImages = [image.id];
				}
			},

			/**
			 * 切换选择模式
			 */
			toggleSelectMode() {
				this.isSelectMode = !this.isSelectMode;
				if (!this.isSelectMode) {
					this.selectedImages = [];
				}
			},

			/**
			 * 切换图片选择状态
			 */
			toggleImageSelection(imageId) {
				const index = this.selectedImages.indexOf(imageId);
				if (index > -1) {
					this.selectedImages.splice(index, 1);
				} else {
					this.selectedImages.push(imageId);
				}

				// 如果没有选中的图片，退出选择模式
				if (this.selectedImages.length === 0) {
					this.isSelectMode = false;
				}
			},

			/**
			 * 检查图片是否被选中
			 */
			isImageSelected(imageId) {
				return this.selectedImages.includes(imageId);
			},

			/**
			 * 显示图片预览
			 */
			showImagePreview(index) {
				this.currentPreviewIndex = index;
				this.showPreview = true;
			},

			/**
			 * 关闭图片预览
			 */
			closePreview() {
				this.showPreview = false;
			},

			/**
			 * 轮播图切换
			 */
			onSwiperChange(e) {
				this.currentPreviewIndex = e.detail.current;
			},

			/**
			 * 处理删除选中图片
			 */
			async handleDeleteSelected() {
				console.log('🗑️ 开始删除操作，选中图片:', this.selectedImages);

				if (this.selectedImages.length === 0) {
					showError('请先选择要删除的图片');
					return;
				}

				const confirmed = await showConfirm(
					'确认删除',
					`确定要删除选中的 ${this.selectedImages.length} 张图片吗？此操作不可恢复。`
				);

				console.log('🗑️ 用户确认删除:', confirmed);

				if (confirmed) {
					await this.deleteImages();
				}
			},

			/**
			 * 删除图片
			 */
			async deleteImages() {
				console.log('🗑️ 执行删除请求，图片IDs:', this.selectedImages);

				try {
					const response = await this.deleteImagesRequest();
					console.log('🗑️ 删除响应:', response);

					if (response.success) {
						showSuccess(`成功删除 ${this.selectedImages.length} 张图片`);
						this.selectedImages = [];
						this.isSelectMode = false;

						// 清除相关缓存（包括所有分页的缓存）
						for (let page = 1; page <= this.pagination.totalPages; page++) {
							const cacheKey = `${this.userInfo.factory_name}_${this.orderNumber}_page${page}`;
							dataCacheManager.removeCache('images', cacheKey);
						}

						// 重置到第一页
						this.pagination.page = 1;

						// 重新加载图片列表
						await this.loadImages(true);
					} else {
						console.error('🗑️ 删除失败:', response);
						showError(response.message || '删除失败');
					}
				} catch (error) {
					console.error('🗑️ 删除图片异常:', error);
					showError('删除失败：' + error.message);
				}
			},

			/**
			 * 删除图片请求
			 */
			async deleteImagesRequest() {
				return new Promise((resolve, reject) => {
					const requestConfig = userManager.createAuthRequest({
						url: urlConfig.getApiUrl('/api/images/delete'),
						method: 'POST',
						data: {
							imageIds: this.selectedImages
						},
						timeout: 15000,
						success: (res) => {
							if (res.statusCode === 200) {
								resolve(res.data);
							} else {
								reject(new Error(`服务器错误 (${res.statusCode})`));
							}
						},
						fail: (err) => {
							console.error('删除图片请求失败:', err);
							reject(new Error('网络连接失败'));
						}
					});

					uni.request(requestConfig);
				});
			},

			/**
			 * 加载图片列表（支持分页）
			 */
			async loadImages(forceRefresh = false) {
				if (!this.orderNumber || !this.userInfo.factory_name) {
					return;
				}

				// 开始性能监控
				const timerName = `loadImages_${this.orderNumber}_page${this.pagination.page}`;

				// 清理可能存在的同名计时器
				if (this.activeTimers.has(timerName)) {
					try {
						performanceMonitor.endTimer(timerName, 'loadTime');
					} catch (error) {
						console.warn('清理旧计时器失败:', error);
					}
					this.activeTimers.delete(timerName);
				}

				// 启动新计时器
				performanceMonitor.startTimer(timerName);
				this.activeTimers.add(timerName);

				this.isLoading = true;

				try {
					// 构建缓存键，包含分页信息
					const cacheKey = `${this.userInfo.factory_name}_${this.orderNumber}_page${this.pagination.page}`;
					const imageData = await dataCacheManager.getData(
						'images',
						cacheKey,
						() => this.getImagesRequest(),
						{ forceRefresh, ttl: 300 } // 5分钟缓存
					);

					if (imageData.success) {
						this.imageList = imageData.data || [];

						// 更新分页信息
						if (imageData.pagination) {
							this.pagination = {
								...this.pagination,
								...imageData.pagination
							};
						}

						console.log(`图片列表加载成功: 第${this.pagination.page}页, ${this.imageList.length}/${this.pagination.total} 张图片`);

						// 记录加载性能
						const loadDuration = performanceMonitor.endTimer(timerName, 'loadTime');
						this.activeTimers.delete(timerName);
						performanceMonitor.recordLoadPerformance('imageList', this.imageList.length, loadDuration);

						// 记录缓存命中情况
						performanceMonitor.recordCacheHit('data', !forceRefresh);
					} else {
						console.error('获取图片列表失败:', imageData.message);
						this.imageList = [];
						this.pagination.total = 0;
						this.pagination.totalPages = 0;
						performanceMonitor.endTimer(timerName, 'loadTime');
						this.activeTimers.delete(timerName);
					}
				} catch (error) {
					console.error('加载图片列表失败:', error);
					this.imageList = [];
					this.pagination.total = 0;
					this.pagination.totalPages = 0;
					showError('加载图片列表失败');
					performanceMonitor.endTimer(timerName, 'loadTime');
					this.activeTimers.delete(timerName);
				} finally {
					this.isLoading = false;
				}
			},

			/**
			 * 获取图片列表请求（支持分页）
			 */
			async getImagesRequest() {
				return new Promise((resolve, reject) => {
					// 构建查询参数
					let queryParams = `page=${this.pagination.page}&limit=${this.pagination.limit}`;
					if (this.currentShippingNumber && this.currentShippingNumber !== 'ALL') {
						queryParams += `&shipping_number=${encodeURIComponent(this.currentShippingNumber)}`;
					}

					const requestConfig = userManager.createAuthRequest({
						url: urlConfig.getApiUrl(`/api/images/order/${encodeURIComponent(this.orderNumber)}?${queryParams}`),
						method: 'GET',
						timeout: 10000,
						success: (res) => {
							if (res.statusCode === 200) {
								resolve(res.data);
							} else {
								reject(new Error(`服务器错误 (${res.statusCode})`));
							}
						},
						fail: (err) => {
							console.error('获取图片列表请求失败:', err);
							reject(new Error('网络连接失败'));
						}
					});

					uni.request(requestConfig);
				});
			},

			/**
			 * 下拉刷新
			 */
			async handleRefresh() {
				this.isRefreshing = true;
				try {
					// 强制刷新，不使用缓存
					await this.loadImages(true);
				} catch (error) {
					console.error('刷新失败:', error);
				} finally {
					this.isRefreshing = false;
				}
			},

			/**
			 * 获取图片URL - 同步版本，用于模板渲染
			 */
			getImageUrl(imagePath, imageId) {
				return urlConfig.getImageUrl(imagePath, imageId);
			},



			/**
			 * 处理图片加载错误
			 */
			handleImageError(e) {
				console.error('图片加载失败:', e);
				console.error('失败的图片URL:', e.target?.src || '未知');
				console.error('图片元素:', e.target);
			},

			/**
			 * 格式化时间
			 */
			formatTime(timeStr) {
				if (!timeStr) return '未知';
				
				try {
					const date = new Date(timeStr);
					return date.toLocaleString();
				} catch (error) {
					return '未知';
				}
			},

			/**
			 * 显示性能报告（开发调试用）
			 */
			showPerformanceReport() {
				const report = performanceMonitor.generateReport();
				console.log(report);

				// 在开发环境中显示性能统计
				if (process.env.NODE_ENV === 'development') {
					uni.showModal({
						title: '性能报告',
						content: report,
						showCancel: false
					});
				}
			},

			/**
			 * 处理图片上传事件
			 */
			handleImageUploaded(data) {
				if (data.orderNumber === this.orderNumber) {
					console.log('收到图片上传事件，刷新图片列表');
					// 清除缓存并刷新图片列表
					const cacheKey = `${this.userInfo.factory_name}_${this.orderNumber}`;
					dataCacheManager.removeCache('images', cacheKey);
					this.loadImages(true);
				}
			},

			/**
			 * 清理所有活动的计时器
			 */
			cleanupActiveTimers() {
				console.log('清理活动计时器，数量:', this.activeTimers.size);
				for (const timerName of this.activeTimers) {
					try {
						performanceMonitor.endTimer(timerName, 'loadTime');
						console.log('已清理计时器:', timerName);
					} catch (error) {
						console.warn('清理计时器失败:', timerName, error);
					}
				}
				this.activeTimers.clear();
			},

			/**
			 * 初始化
			 */
			async init() {
				// 使用用户管理器检查登录状态
				if (userManager.requireLogin()) {
					this.userInfo = userManager.getUserInfo();
					// 先加载发货单号列表
					await this.loadShippingNumbers();
					// 再加载图片列表
					this.loadImages();
				}
				// 如果未登录，requireLogin会自动跳转到登录页面
			}
		},

		onLoad(options) {
			this.orderNumber = options.orderNumber || '';
			if (!this.orderNumber) {
				showError('订单号不能为空');
				uni.navigateBack();
				return;
			}
			this.init();

			// 监听图片上传事件
			uni.$on('imageUploaded', this.handleImageUploaded);
		},

		onShow() {
			// 页面显示时强制刷新图片列表（不使用缓存）
			if (this.orderNumber && this.userInfo.factory_name) {
				// 清除所有分页的缓存，确保获取最新数据
				for (let page = 1; page <= 10; page++) { // 清除前10页的缓存
					const cacheKey = `${this.userInfo.factory_name}_${this.orderNumber}_page${page}`;
					dataCacheManager.removeCache('images', cacheKey);
				}

				// 重置到第一页并强制刷新
				this.pagination.page = 1;
				this.loadImages(true);
			}
		},

		onBackPress() {
			// 如果在选择模式，先退出选择模式
			if (this.isSelectMode) {
				this.isSelectMode = false;
				this.selectedImages = [];
				return true; // 阻止默认返回行为
			}

			// 清理活动的计时器
			this.cleanupActiveTimers();

			return false; // 允许默认返回行为
		},

		onUnload() {
			// 清理事件监听
			uni.$off('imageUploaded', this.handleImageUploaded);

			// 清理所有活动的计时器
			this.cleanupActiveTimers();
		}
	}
</script>

<style scoped>
	.image-manage-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 20rpx;
	}

	.header {
		background: linear-gradient(135deg, #667eea, #764ba2);
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
	}

	/* 发货单号选择器样式 */
	.shipping-selector {
		margin: 0 20rpx 30rpx;
		background: white;
		border-radius: 20rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.selector-title {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.title-icon {
		font-size: 32rpx;
		margin-right: 10rpx;
	}

	.title-text {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
	}

	.shipping-tabs {
		display: flex;
		flex-wrap: wrap;
		gap: 15rpx;
	}

	.shipping-tab {
		padding: 15rpx 25rpx;
		background: #f8f9fa;
		border-radius: 25rpx;
		border: 2rpx solid #e9ecef;
		transition: all 0.3s ease;
	}

	.shipping-tab.active {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-color: #667eea;
		color: white;
	}

	.tab-text {
		font-size: 26rpx;
		font-weight: 500;
	}

	.shipping-tab.active .tab-text {
		color: white;
	}

	/* 当前发货单号显示 */
	.current-shipping {
		margin: 0 20rpx 30rpx;
		padding: 20rpx 30rpx;
		background: #f8f9fa;
		border-radius: 15rpx;
		border-left: 4rpx solid #667eea;
	}

	.shipping-info {
		font-size: 26rpx;
		color: #666;
		font-weight: 500;
	}

	.order-info {
		display: flex;
		align-items: center;
		flex: 1;
	}

	.order-icon {
		font-size: 50rpx;
		margin-right: 20rpx;
	}

	.order-details {
		display: flex;
		flex-direction: column;
	}

	.order-label {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.8);
		margin-bottom: 8rpx;
	}

	.order-number {
		font-size: 36rpx;
		font-weight: bold;
		color: #ffffff;
	}

	.image-count {
		background: rgba(255, 255, 255, 0.2);
		border-radius: 30rpx;
		padding: 15rpx 25rpx;
	}

	.count-text {
		font-size: 26rpx;
		color: #ffffff;
	}

	.action-section {
		display: flex;
		gap: 20rpx;
		margin-bottom: 30rpx;
	}

	.action-btn {
		flex: 1;
		height: 80rpx;
		border: none;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 15rpx;
		font-size: 28rpx;
		font-weight: bold;
	}

	.add-btn {
		background: linear-gradient(135deg, #4CAF50, #45a049);
		color: #ffffff;
	}

	.select-btn {
		background: linear-gradient(135deg, #2196F3, #1976D2);
		color: #ffffff;
	}

	.select-btn.active {
		background: linear-gradient(135deg, #FF9800, #F57C00);
	}

	.delete-btn {
		background: linear-gradient(135deg, #f44336, #d32f2f);
		color: #ffffff;
	}

	.delete-btn.disabled {
		background: #cccccc;
		color: #999999;
	}

	.btn-icon {
		font-size: 32rpx;
	}

	.select-tip {
		background: #e3f2fd;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		border-left: 6rpx solid #2196F3;
	}

	.tip-text {
		font-size: 26rpx;
		color: #1976D2;
	}

	.image-grid-section {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 30rpx;
		flex: 1;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	/* 图片网格样式 */
	.image-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20rpx;
		padding-bottom: 20rpx;
	}

	.image-item {
		position: relative;
		background: #f8f9fa;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		aspect-ratio: 1;
	}

	.select-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.3);
		z-index: 2;
		display: flex;
		align-items: flex-start;
		justify-content: flex-end;
		padding: 20rpx;
	}

	.select-checkbox {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		border: 4rpx solid #fff;
		background: rgba(255, 255, 255, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
	}

	.select-checkbox.selected {
		background: #007aff;
		border-color: #007aff;
	}

	.checkbox-icon {
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
	}

	.image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.image-info {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
		padding: 30rpx 20rpx 20rpx;
		color: #fff;
	}

	.image-name {
		font-size: 24rpx;
		font-weight: 500;
		margin-bottom: 8rpx;
		display: block;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.image-time {
		font-size: 20rpx;
		opacity: 0.8;
		display: block;
	}

	/* 分页样式 */
	.pagination-section {
		margin-top: 30rpx;
		padding-top: 30rpx;
		border-top: 2rpx solid #f0f0f0;
	}

	.pagination-info {
		text-align: center;
		margin-bottom: 20rpx;
	}

	.page-info {
		font-size: 26rpx;
		color: #333;
		margin-right: 20rpx;
	}

	.total-info {
		font-size: 24rpx;
		color: #666;
	}

	.pagination-controls {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 20rpx;
	}

	.page-btn {
		padding: 16rpx 32rpx;
		border: 2rpx solid #007aff;
		border-radius: 8rpx;
		background: #fff;
		color: #007aff;
		font-size: 24rpx;
		transition: all 0.3s ease;
	}

	.page-btn:not([disabled]):active {
		background: #007aff;
		color: #fff;
	}

	.page-btn[disabled] {
		border-color: #ddd;
		color: #ccc;
		background: #f5f5f5;
	}

	.page-numbers {
		display: flex;
		gap: 10rpx;
	}

	.page-number {
		width: 60rpx;
		height: 60rpx;
		border-radius: 8rpx;
		border: 2rpx solid #ddd;
		background: #fff;
		color: #333;
		font-size: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
	}

	.page-number.current {
		background: #007aff;
		border-color: #007aff;
		color: #fff;
	}

	.page-number:not(.current):active {
		background: #f0f0f0;
	}

	.loading-section {
		text-align: center;
		padding: 80rpx 0;
	}

	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #667eea;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin: 0 auto 20rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #999999;
	}

	/* 虚拟滚动组件的样式已在组件内部定义 */

	.empty-state {
		text-align: center;
		padding: 100rpx 0;
	}

	.empty-icon {
		font-size: 100rpx;
		margin-bottom: 30rpx;
		display: block;
	}

	.empty-text {
		font-size: 32rpx;
		color: #666666;
		margin-bottom: 15rpx;
		display: block;
	}

	.empty-hint {
		font-size: 26rpx;
		color: #999999;
		display: block;
	}

	.preview-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.9);
		z-index: 9999;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.preview-content {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
	}

	.preview-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 40rpx 30rpx;
		background: rgba(0, 0, 0, 0.5);
	}

	.preview-title {
		font-size: 32rpx;
		color: #ffffff;
		font-weight: bold;
	}

	.close-btn {
		font-size: 40rpx;
		color: #ffffff;
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.preview-swiper {
		flex: 1;
	}

	.preview-image {
		width: 100%;
		height: 100%;
	}

	.preview-info {
		padding: 30rpx;
		background: rgba(0, 0, 0, 0.5);
		text-align: center;
	}

	.preview-name {
		font-size: 28rpx;
		color: #ffffff;
		margin-bottom: 10rpx;
		display: block;
	}

	.preview-index {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.8);
		display: block;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
</style>
