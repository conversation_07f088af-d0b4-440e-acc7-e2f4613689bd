
.image-manage-container.data-v-78a14e75 {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 20rpx;
}
.header.data-v-78a14e75 {
		background: linear-gradient(135deg, #667eea, #764ba2);
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}
.order-info.data-v-78a14e75 {
		display: flex;
		align-items: center;
		flex: 1;
}
.order-icon.data-v-78a14e75 {
		font-size: 50rpx;
		margin-right: 20rpx;
}
.order-details.data-v-78a14e75 {
		display: flex;
		flex-direction: column;
}
.order-label.data-v-78a14e75 {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.8);
		margin-bottom: 8rpx;
}
.order-number.data-v-78a14e75 {
		font-size: 36rpx;
		font-weight: bold;
		color: #ffffff;
}
.image-count.data-v-78a14e75 {
		background: rgba(255, 255, 255, 0.2);
		border-radius: 30rpx;
		padding: 15rpx 25rpx;
}
.count-text.data-v-78a14e75 {
		font-size: 26rpx;
		color: #ffffff;
}
.action-section.data-v-78a14e75 {
		display: flex;
		gap: 20rpx;
		margin-bottom: 30rpx;
}
.action-btn.data-v-78a14e75 {
		flex: 1;
		height: 80rpx;
		border: none;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 15rpx;
		font-size: 28rpx;
		font-weight: bold;
}
.add-btn.data-v-78a14e75 {
		background: linear-gradient(135deg, #4CAF50, #45a049);
		color: #ffffff;
}
.select-btn.data-v-78a14e75 {
		background: linear-gradient(135deg, #2196F3, #1976D2);
		color: #ffffff;
}
.select-btn.active.data-v-78a14e75 {
		background: linear-gradient(135deg, #FF9800, #F57C00);
}
.delete-btn.data-v-78a14e75 {
		background: linear-gradient(135deg, #f44336, #d32f2f);
		color: #ffffff;
}
.delete-btn.disabled.data-v-78a14e75 {
		background: #cccccc;
		color: #999999;
}
.btn-icon.data-v-78a14e75 {
		font-size: 32rpx;
}
.select-tip.data-v-78a14e75 {
		background: #e3f2fd;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		border-left: 6rpx solid #2196F3;
}
.tip-text.data-v-78a14e75 {
		font-size: 26rpx;
		color: #1976D2;
}
.image-grid-section.data-v-78a14e75 {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 30rpx;
		flex: 1;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

	/* 图片网格样式 */
.image-grid.data-v-78a14e75 {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20rpx;
		padding-bottom: 20rpx;
}
.image-item.data-v-78a14e75 {
		position: relative;
		background: #f8f9fa;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		aspect-ratio: 1;
}
.select-overlay.data-v-78a14e75 {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.3);
		z-index: 2;
		display: flex;
		align-items: flex-start;
		justify-content: flex-end;
		padding: 20rpx;
}
.select-checkbox.data-v-78a14e75 {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		border: 4rpx solid #fff;
		background: rgba(255, 255, 255, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
}
.select-checkbox.selected.data-v-78a14e75 {
		background: #007aff;
		border-color: #007aff;
}
.checkbox-icon.data-v-78a14e75 {
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
}
.image.data-v-78a14e75 {
		width: 100%;
		height: 100%;
		object-fit: cover;
}
.image-info.data-v-78a14e75 {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
		padding: 30rpx 20rpx 20rpx;
		color: #fff;
}
.image-name.data-v-78a14e75 {
		font-size: 24rpx;
		font-weight: 500;
		margin-bottom: 8rpx;
		display: block;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
}
.image-time.data-v-78a14e75 {
		font-size: 20rpx;
		opacity: 0.8;
		display: block;
}

	/* 分页样式 */
.pagination-section.data-v-78a14e75 {
		margin-top: 30rpx;
		padding-top: 30rpx;
		border-top: 2rpx solid #f0f0f0;
}
.pagination-info.data-v-78a14e75 {
		text-align: center;
		margin-bottom: 20rpx;
}
.page-info.data-v-78a14e75 {
		font-size: 26rpx;
		color: #333;
		margin-right: 20rpx;
}
.total-info.data-v-78a14e75 {
		font-size: 24rpx;
		color: #666;
}
.pagination-controls.data-v-78a14e75 {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 20rpx;
}
.page-btn.data-v-78a14e75 {
		padding: 16rpx 32rpx;
		border: 2rpx solid #007aff;
		border-radius: 8rpx;
		background: #fff;
		color: #007aff;
		font-size: 24rpx;
		transition: all 0.3s ease;
}
.page-btn.data-v-78a14e75:not([disabled]):active {
		background: #007aff;
		color: #fff;
}
.page-btn[disabled].data-v-78a14e75 {
		border-color: #ddd;
		color: #ccc;
		background: #f5f5f5;
}
.page-numbers.data-v-78a14e75 {
		display: flex;
		gap: 10rpx;
}
.page-number.data-v-78a14e75 {
		width: 60rpx;
		height: 60rpx;
		border-radius: 8rpx;
		border: 2rpx solid #ddd;
		background: #fff;
		color: #333;
		font-size: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
}
.page-number.current.data-v-78a14e75 {
		background: #007aff;
		border-color: #007aff;
		color: #fff;
}
.page-number.data-v-78a14e75:not(.current):active {
		background: #f0f0f0;
}
.loading-section.data-v-78a14e75 {
		text-align: center;
		padding: 80rpx 0;
}
.loading-spinner.data-v-78a14e75 {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #667eea;
		border-radius: 50%;
		animation: spin-78a14e75 1s linear infinite;
		margin: 0 auto 20rpx;
}
.loading-text.data-v-78a14e75 {
		font-size: 28rpx;
		color: #999999;
}

	/* 虚拟滚动组件的样式已在组件内部定义 */
.empty-state.data-v-78a14e75 {
		text-align: center;
		padding: 100rpx 0;
}
.empty-icon.data-v-78a14e75 {
		font-size: 100rpx;
		margin-bottom: 30rpx;
		display: block;
}
.empty-text.data-v-78a14e75 {
		font-size: 32rpx;
		color: #666666;
		margin-bottom: 15rpx;
		display: block;
}
.empty-hint.data-v-78a14e75 {
		font-size: 26rpx;
		color: #999999;
		display: block;
}
.preview-modal.data-v-78a14e75 {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.9);
		z-index: 9999;
		display: flex;
		align-items: center;
		justify-content: center;
}
.preview-content.data-v-78a14e75 {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
}
.preview-header.data-v-78a14e75 {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 40rpx 30rpx;
		background: rgba(0, 0, 0, 0.5);
}
.preview-title.data-v-78a14e75 {
		font-size: 32rpx;
		color: #ffffff;
		font-weight: bold;
}
.close-btn.data-v-78a14e75 {
		font-size: 40rpx;
		color: #ffffff;
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.preview-swiper.data-v-78a14e75 {
		flex: 1;
}
.preview-image.data-v-78a14e75 {
		width: 100%;
		height: 100%;
}
.preview-info.data-v-78a14e75 {
		padding: 30rpx;
		background: rgba(0, 0, 0, 0.5);
		text-align: center;
}
.preview-name.data-v-78a14e75 {
		font-size: 28rpx;
		color: #ffffff;
		margin-bottom: 10rpx;
		display: block;
}
.preview-index.data-v-78a14e75 {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.8);
		display: block;
}
@keyframes spin-78a14e75 {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
