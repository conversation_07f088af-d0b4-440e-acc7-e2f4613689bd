# H5环境测试指南

## 问题原因
在H5环境下，`uni.chooseImage`的`sourceType: ['camera']`会尝试调用浏览器的摄像头API，这可能导致：
1. 页面刷新
2. 浏览器兼容性问题
3. 权限请求失败

## 解决方案
已对拍照功能进行H5环境适配：

### 1. ImagePicker组件适配
- H5环境下点击"拍照"按钮会自动调用相册选择
- 非H5环境保持原有拍照功能

### 2. Camera页面适配
- H5环境下拍照功能重定向到相册选择
- 避免浏览器摄像头API的兼容性问题

### 3. 代理配置更新
- H5开发环境代理目标改为 `http://localhost:3001`
- 与本地后端端口保持一致

## 测试步骤

### 1. 启动本地后端
```bash
cd server
npm run dev:local:watch
```

### 2. 启动前端H5
```bash
cd front
npm run dev:h5
```

### 3. 浏览器访问
通过uni-app开发工具或直接在浏览器中访问H5版本

## H5环境特点

### 支持的功能
✅ 相册选择图片
✅ 图片预览
✅ 文字识别
✅ 历史记录查看
✅ 用户登录/登出

### 限制的功能
❌ 直接拍照（重定向到相册选择）
❌ 某些原生API功能

## 浏览器兼容性
- Chrome: 完全支持
- Firefox: 完全支持
- Safari: 基本支持
- Edge: 完全支持

## 调试建议
1. 打开浏览器开发者工具查看控制台日志
2. 检查网络请求是否正常
3. 确认后端服务运行在3001端口
4. 检查跨域配置是否正确

## 注意事项
1. H5环境下的文件上传可能比小程序慢
2. 某些移动端浏览器可能有兼容性问题
3. 建议使用桌面浏览器进行开发测试
4. 生产环境仍建议使用微信小程序版本
