"use strict";
const common_vendor = require("../common/vendor.js");
const WAREHOUSE_USERS = [
  {
    id: 1,
    username: "wh001",
    password: "wh123456",
    displayName: "管理员",
    department: "仓储部",
    permissions: ["read", "write", "delete"]
  },
  {
    id: 2,
    username: "wh002",
    password: "wh123",
    displayName: "仓管1",
    department: "仓储部",
    permissions: ["read", "write", "delete"]
  },
  {
    id: 3,
    username: "wh003",
    password: "wh456",
    displayName: "仓管2",
    department: "仓储部",
    permissions: ["read", "write", "delete"]
  }
];
const WAREHOUSE_SESSION_KEY = "warehouse_user_session";
const WAREHOUSE_LOGIN_TIME_KEY = "warehouse_login_time";
class WarehouseUserManager {
  constructor() {
    this.currentUser = null;
    this.isLoggedIn = false;
    this.init();
  }
  /**
   * 初始化，检查本地存储的登录状态
   */
  init() {
    try {
      const savedUser = common_vendor.index.getStorageSync(WAREHOUSE_SESSION_KEY);
      const loginTime = common_vendor.index.getStorageSync(WAREHOUSE_LOGIN_TIME_KEY);
      if (savedUser && loginTime) {
        const now = (/* @__PURE__ */ new Date()).getTime();
        const loginTimestamp = new Date(loginTime).getTime();
        const hoursDiff = (now - loginTimestamp) / (1e3 * 60 * 60);
        if (hoursDiff < 24) {
          this.currentUser = savedUser;
          this.isLoggedIn = true;
          common_vendor.index.__f__("log", "at utils/warehouseUsers.js:65", "✅ 仓库管理用户自动登录:", savedUser.displayName);
        } else {
          this.logout();
          common_vendor.index.__f__("log", "at utils/warehouseUsers.js:69", "⚠️ 仓库管理登录已过期，需要重新登录");
        }
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/warehouseUsers.js:73", "❌ 初始化仓库管理用户状态失败:", error);
      this.logout();
    }
  }
  /**
   * 用户登录验证
   * @param {string} username 用户名
   * @param {string} password 密码
   * @returns {Object} 登录结果
   */
  login(username, password) {
    try {
      const user = WAREHOUSE_USERS.find(
        (u) => u.username === username.trim() && u.password === password.trim()
      );
      if (!user) {
        return {
          success: false,
          message: "用户名或密码错误"
        };
      }
      const userSession = {
        id: user.id,
        username: user.username,
        displayName: user.displayName,
        department: user.department,
        permissions: user.permissions,
        loginTime: (/* @__PURE__ */ new Date()).toISOString()
      };
      common_vendor.index.setStorageSync(WAREHOUSE_SESSION_KEY, userSession);
      common_vendor.index.setStorageSync(WAREHOUSE_LOGIN_TIME_KEY, userSession.loginTime);
      this.currentUser = userSession;
      this.isLoggedIn = true;
      common_vendor.index.__f__("log", "at utils/warehouseUsers.js:116", "✅ 仓库管理用户登录成功:", userSession.displayName);
      return {
        success: true,
        message: "登录成功",
        user: userSession
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/warehouseUsers.js:125", "❌ 仓库管理用户登录失败:", error);
      return {
        success: false,
        message: "登录过程中发生错误"
      };
    }
  }
  /**
   * 用户登出
   */
  logout() {
    try {
      common_vendor.index.removeStorageSync(WAREHOUSE_SESSION_KEY);
      common_vendor.index.removeStorageSync(WAREHOUSE_LOGIN_TIME_KEY);
      this.currentUser = null;
      this.isLoggedIn = false;
      common_vendor.index.__f__("log", "at utils/warehouseUsers.js:146", "✅ 仓库管理用户登出成功");
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/warehouseUsers.js:150", "❌ 仓库管理用户登出失败:", error);
      return false;
    }
  }
  /**
   * 获取当前用户信息
   * @returns {Object|null} 用户信息
   */
  getCurrentUser() {
    return this.currentUser;
  }
  /**
   * 检查是否已登录
   * @returns {boolean} 登录状态
   */
  checkLoginStatus() {
    return this.isLoggedIn && this.currentUser !== null;
  }
  /**
   * 检查用户权限
   * @param {string} permission 权限名称
   * @returns {boolean} 是否有权限
   */
  hasPermission(permission) {
    if (!this.isLoggedIn || !this.currentUser) {
      return false;
    }
    return this.currentUser.permissions.includes(permission);
  }
  /**
   * 要求登录（如果未登录则跳转到登录页面）
   * @returns {boolean} 是否已登录
   */
  requireLogin() {
    if (!this.checkLoginStatus()) {
      common_vendor.index.navigateTo({
        url: "/pages/warehouseLogin/warehouseLogin",
        fail: (err) => {
          common_vendor.index.__f__("error", "at utils/warehouseUsers.js:192", "跳转到仓库管理登录页面失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "error"
          });
        }
      });
      return false;
    }
    return true;
  }
  /**
   * 获取所有可用用户列表（仅用于开发调试）
   * @returns {Array} 用户列表（不包含密码）
   */
  getAvailableUsers() {
    return WAREHOUSE_USERS.map((user) => ({
      id: user.id,
      username: user.username,
      displayName: user.displayName,
      department: user.department,
      permissions: user.permissions
    }));
  }
}
const warehouseUserManager = new WarehouseUserManager();
exports.warehouseUserManager = warehouseUserManager;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/warehouseUsers.js.map
