# 发货单号功能实现说明

## 功能概述

实现了订单号对应多个发货单号的功能，用户可以在图片管理界面切换不同的发货单号，上传图片时会关联对应的发货单号。

## 数据库表结构

### shipping_detail 表
- `order_no`: 订单号
- `shipping_no`: 发货单号
- `receiver`: 接收方（工厂名称）

### img_info 表
- 新增 `shipping_number` 字段：存储发货单号

## 后端接口修改

### 1. 新增获取发货单号列表接口
**接口**: `GET /api/orders/:orderNumber/shipping-numbers`

**功能**: 根据订单号获取该订单对应的所有发货单号

**逻辑**:
```sql
SELECT DISTINCT shipping_no
FROM shipping_detail
WHERE receiver = '用户工厂名' AND order_no = '订单号'
AND shipping_no IS NOT NULL AND shipping_no != ''
ORDER BY shipping_no
```

**返回**: 发货单号数组，如果没有则返回 `['DEFAULT']`

### 2. 修改图片上传接口
**接口**: `POST /api/images/upload`

**新增参数**: `shipping_number` - 发货单号

**数据库操作**:
```sql
INSERT INTO Img_Info
(factory_name, order_number, image_name, image_path, file_size, shipping_number, upload_date, created_at)
VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
```

### 3. 修改图片列表查询接口
**接口**: `GET /api/images/order/:orderNumber`

**新增参数**: `shipping_number` - 可选，用于过滤特定发货单号的图片

**查询逻辑**:
- 如果 `shipping_number` 为空或 'ALL'，返回所有图片
- 否则只返回指定发货单号的图片

## 前端界面修改

### 1. 图片管理页面 (imageManage.vue)

#### 新增UI组件
- **发货单号选择器**: 当有多个发货单号时显示选项卡
- **当前发货单号显示**: 当只有一个发货单号时显示信息

#### 数据结构
```javascript
data() {
  return {
    // 发货单号相关
    shippingNumbers: [],        // 发货单号列表
    currentShippingNumber: 'ALL' // 当前选中的发货单号
  }
}
```

#### 新增方法
- `loadShippingNumbers()`: 加载发货单号列表
- `switchShippingNumber(shippingNumber)`: 切换发货单号

#### 修改方法
- `goToCamera()`: 传递发货单号参数
- `getImagesRequest()`: 添加发货单号查询参数
- `init()`: 先加载发货单号列表再加载图片

### 2. 拍照上传页面 (camera.vue)

#### 数据结构
```javascript
data() {
  return {
    shippingNumber: 'DEFAULT' // 发货单号
  }
}
```

#### 修改方法
- `onLoad(options)`: 接收发货单号参数
- `uploadSingleImage()`: 上传时包含发货单号

## 业务流程

### 1. 进入图片管理页面
1. 从订单管理页面跳转，传递订单号
2. 加载该订单对应的发货单号列表
3. 如果有多个发货单号，显示选择器，默认选择"全部"
4. 如果只有一个发货单号，直接显示该发货单号
5. 加载图片列表

### 2. 切换发货单号
1. 用户点击不同的发货单号选项卡
2. 更新当前选中的发货单号
3. 重新加载图片列表（只显示该发货单号的图片）

### 3. 上传图片
1. 点击"新增"按钮跳转到拍照页面
2. 传递当前选中的发货单号（如果选择"全部"，则使用第一个发货单号）
3. 上传图片时将发货单号一起提交
4. 图片保存到数据库时关联发货单号

## 样式设计

### 发货单号选择器
- 选项卡式设计，支持横向滚动
- 激活状态使用渐变背景色
- 响应式布局，适配不同屏幕尺寸

### 当前发货单号显示
- 简洁的信息条样式
- 左侧彩色边框突出显示

## 权限控制

- 用户只能查看自己工厂的发货单号
- 基于 `shipping_detail.receiver` 字段进行权限过滤
- 确保数据隔离和安全性

## 兼容性处理

### 默认值处理
- 如果订单没有发货单号，使用 'DEFAULT' 作为默认值
- 确保旧数据的兼容性

### 错误处理
- 网络请求失败时的降级处理
- 数据异常时的友好提示

## 测试场景

### 1. 单个发货单号
- 订单只有一个发货单号
- 界面显示当前发货单号信息
- 上传图片关联该发货单号

### 2. 多个发货单号
- 订单有多个发货单号
- 界面显示选择器，包含"全部"选项
- 可以切换查看不同发货单号的图片
- 上传时关联当前选中的发货单号

### 3. 无发货单号
- 订单在 shipping_detail 表中没有记录
- 使用 'DEFAULT' 作为默认发货单号
- 功能正常运行

## 性能优化

### 缓存策略
- 发货单号列表缓存，避免重复请求
- 图片列表按发货单号分别缓存

### 请求优化
- 发货单号列表只在页面初始化时加载一次
- 切换发货单号时只重新加载图片列表

## 后续扩展

### 可能的功能扩展
1. 发货单号搜索功能
2. 批量操作特定发货单号的图片
3. 发货单号统计信息显示
4. 发货单号相关的报表功能
