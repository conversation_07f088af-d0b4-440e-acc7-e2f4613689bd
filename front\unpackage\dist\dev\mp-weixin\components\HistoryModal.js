"use strict";
const common_vendor = require("../common/vendor.js");
const utils_apiService = require("../utils/apiService.js");
const utils_helpers = require("../utils/helpers.js");
const utils_warehouseUsers = require("../utils/warehouseUsers.js");
const utils_urlConfig = require("../utils/urlConfig.js");
const _sfc_main = {
  name: "HistoryModal",
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      records: [],
      currentDate: ""
    };
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadTodayHistory();
        this.updateCurrentDate();
      }
    }
  },
  methods: {
    /**
     * 加载今日历史记录
     */
    async loadTodayHistory() {
      this.loading = true;
      try {
        common_vendor.index.__f__("log", "at components/HistoryModal.vue:144", "📋 开始加载今日历史记录...");
        const currentUser = utils_warehouseUsers.warehouseUserManager.getCurrentUser();
        if (!currentUser) {
          utils_helpers.showError("请先登录");
          this.records = [];
          return;
        }
        const username = currentUser.username;
        common_vendor.index.__f__("log", "at components/HistoryModal.vue:155", `📋 查询用户 ${username} 的历史记录`);
        const result = await utils_apiService.apiService.getTodayHistory(username);
        this.records = result.data || [];
        common_vendor.index.__f__("log", "at components/HistoryModal.vue:159", `✅ 加载完成，共 ${this.records.length} 条记录`);
        if (this.records.length > 0) {
          utils_helpers.showSuccess(`加载了 ${this.records.length} 条今日记录`);
        } else {
          utils_helpers.showSuccess("今日暂无上传记录");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at components/HistoryModal.vue:167", "❌ 加载历史记录失败:", error);
        utils_helpers.showError("加载历史记录失败: " + error.message);
        this.records = [];
      } finally {
        this.loading = false;
      }
    },
    /**
     * 更新当前日期显示
     */
    updateCurrentDate() {
      const now = /* @__PURE__ */ new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      const weekdays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
      const weekday = weekdays[now.getDay()];
      this.currentDate = `${year}年${month}月${day}日 ${weekday}`;
    },
    /**
     * 获取分类样式类名
     */
    getCategoryClass(type) {
      const classMap = {
        "general": "category-general",
        "registration": "category-registration",
        "carton_info": "category-carton",
        "unknown": "category-unknown"
      };
      return classMap[type] || "category-general";
    },
    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
      if (!bytes || bytes === 0)
        return "未知";
      const units = ["B", "KB", "MB", "GB"];
      let size = bytes;
      let unitIndex = 0;
      while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
      }
      return `${size.toFixed(1)} ${units[unitIndex]}`;
    },
    /**
     * 获取图片URL
     */
    getImageUrl(imagePath) {
      return utils_urlConfig.urlConfig.getHistoryImageUrl(imagePath);
    },
    /**
     * 预览图片
     */
    previewImage(record) {
      common_vendor.index.__f__("log", "at components/HistoryModal.vue:231", "🖼️ 预览图片:", record.ImageName);
      const imageUrl = this.getImageUrl(record.ImagePath);
      common_vendor.index.previewImage({
        urls: [imageUrl],
        current: imageUrl,
        success: () => {
          common_vendor.index.__f__("log", "at components/HistoryModal.vue:239", "✅ 图片预览成功");
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at components/HistoryModal.vue:242", "❌ 图片预览失败:", error);
          utils_helpers.showError("图片预览失败");
        }
      });
    },
    /**
     * 图片加载错误处理
     */
    onImageError(record, index) {
      common_vendor.index.__f__("error", "at components/HistoryModal.vue:252", "❌ 图片加载失败:", record.ImagePath);
      this.records[index].hasImage = false;
    },
    /**
     * 图片加载成功处理
     */
    onImageLoad(record, index) {
      common_vendor.index.__f__("log", "at components/HistoryModal.vue:261", "✅ 图片加载成功:", record.ImagePath);
      this.records[index].hasImage = true;
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit("close");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.visible
  }, $props.visible ? common_vendor.e({
    b: common_vendor.o((...args) => $options.closeModal && $options.closeModal(...args)),
    c: common_vendor.o((...args) => $options.closeModal && $options.closeModal(...args)),
    d: common_vendor.t($data.currentDate),
    e: common_vendor.t($data.records.length),
    f: $data.loading
  }, $data.loading ? {} : $data.records.length === 0 ? {} : {
    h: common_vendor.f($data.records, (record, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(record.ImageName),
        b: common_vendor.t(record.UploadTime),
        c: common_vendor.t(record.category),
        d: common_vendor.n($options.getCategoryClass(record.recognitionType)),
        e: record.CartonNumber
      }, record.CartonNumber ? {
        f: common_vendor.t(record.CartonNumber)
      } : {}, {
        g: common_vendor.t($options.formatFileSize(record.fileSize)),
        h: record.hasImage !== false
      }, record.hasImage !== false ? {
        i: $options.getImageUrl(record.ImagePath),
        j: common_vendor.o(($event) => $options.onImageError(record, index), index),
        k: common_vendor.o(($event) => $options.onImageLoad(record, index), index),
        l: common_vendor.o(($event) => $options.previewImage(record), index)
      } : {}, {
        m: index
      });
    })
  }, {
    g: $data.records.length === 0,
    i: common_vendor.o((...args) => $options.loadTodayHistory && $options.loadTodayHistory(...args)),
    j: common_vendor.o((...args) => $options.closeModal && $options.closeModal(...args))
  }) : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-13e95ff7"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/HistoryModal.js.map
