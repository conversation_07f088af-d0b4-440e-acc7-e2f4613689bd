
	/* 弹窗容器 */
.history-modal.data-v-13e95ff7 {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 100;
		display: flex;
		align-items: center;
		justify-content: center;
}

	/* 遮罩层 */
.modal-overlay.data-v-13e95ff7 {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
}

	/* 弹窗内容 */
.modal-content.data-v-13e95ff7 {
		position: relative;
		width: 90%;
		max-width: 700rpx;
		max-height: 80%;
		background: #ffffff;
		border-radius: 20rpx;
		display: flex;
		flex-direction: column;
		overflow: hidden;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

	/* 标题栏 */
.modal-header.data-v-13e95ff7 {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		border-bottom: 2rpx solid #f0f0f0;
		background: #fafafa;
}
.modal-title.data-v-13e95ff7 {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
}
.close-btn.data-v-13e95ff7 {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		background: #f5f5f5;
}
.close-icon.data-v-13e95ff7 {
		font-size: 40rpx;
		color: #666666;
		font-weight: bold;
}

	/* 日期信息 */
.date-info.data-v-13e95ff7 {
		padding: 20rpx 30rpx;
		background: #f8f9fa;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 2rpx solid #f0f0f0;
}
.date-text.data-v-13e95ff7 {
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
}
.count-text.data-v-13e95ff7 {
		font-size: 24rpx;
		color: #666666;
}

	/* 加载状态 */
.loading-container.data-v-13e95ff7 {
		padding: 80rpx 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
}
.loading-spinner.data-v-13e95ff7 {
		width: 60rpx;
		height: 60rpx;
		border: 6rpx solid #f3f3f3;
		border-top: 6rpx solid #007aff;
		border-radius: 50%;
		animation: spin-13e95ff7 1s linear infinite;
		margin-bottom: 20rpx;
}
@keyframes spin-13e95ff7 {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.loading-text.data-v-13e95ff7 {
		font-size: 28rpx;
		color: #666666;
}

	/* 空状态 */
.empty-state.data-v-13e95ff7 {
		padding: 80rpx 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
}
.empty-icon.data-v-13e95ff7 {
		font-size: 120rpx;
		margin-bottom: 20rpx;
		opacity: 0.5;
		display: block;
}
.empty-text.data-v-13e95ff7 {
		font-size: 28rpx;
		color: #999999;
}

	/* 记录列表容器 */
.records-wrapper.data-v-13e95ff7 {
		flex: 1;
		padding: 0 20rpx;
		max-height: 800rpx;
		position: relative;
		overflow: hidden;
}

	/* 横向滚动容器 */
.records-horizontal-scroll.data-v-13e95ff7 {
		width: 100%;
		height: 100%;
		white-space: nowrap;
}

	/* 竖向滚动容器 */
.records-vertical-scroll.data-v-13e95ff7 {
		width: 100%;
		height: 800rpx;
		min-width: 500rpx; /* 调整最小宽度，适合移动设备 */
}

	/* 记录内容容器 */
.records-content.data-v-13e95ff7 {
		width: 100%;
		min-width: 500rpx; /* 调整最小宽度，适合移动设备 */
		padding-bottom: 20rpx;
}

	/* 记录项 */
.record-item.data-v-13e95ff7 {
		display: flex;
		background: #ffffff;
		border-radius: 15rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
		border: 2rpx solid #f0f0f0;
		min-width: 480rpx; /* 调整最小宽度，适合移动设备 */
		width: 100%;
		box-sizing: border-box;
		align-items: flex-start; /* 确保内容顶部对齐 */
}

	/* 记录信息 */
.record-info.data-v-13e95ff7 {
		flex: 1;
		margin-right: 20rpx;
		max-width: 420rpx; /* 限制信息区域最大宽度，为图片预留空间 */
		min-width: 300rpx; /* 设置最小宽度，保证内容可读性 */
}
.record-header.data-v-13e95ff7 {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15rpx;
}
.record-title.data-v-13e95ff7 {
		font-size: 28rpx;
		font-weight: 500;
		color: #333333;
		flex: 1;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		max-width: 350rpx; /* 限制最大宽度，确保右边图片能显示 */
}
.record-time.data-v-13e95ff7 {
		font-size: 24rpx;
		color: #666666;
		margin-left: 10rpx;
}

	/* 详情项 */
.record-details.data-v-13e95ff7 {
		display: flex;
		flex-direction: column;
		gap: 8rpx;
}
.detail-item.data-v-13e95ff7 {
		display: flex;
		align-items: center;
}
.detail-label.data-v-13e95ff7 {
		font-size: 24rpx;
		color: #666666;
		width: 120rpx;
		flex-shrink: 0;
}
.detail-value.data-v-13e95ff7 {
		font-size: 24rpx;
		color: #333333;
		flex: 1;
}

	/* 识别结果文本特殊样式 */
.detail-value.result-text.data-v-13e95ff7 {
		word-break: break-all;
		white-space: normal;
		line-height: 1.4;
		max-width: 300rpx;
		overflow: hidden;
		text-overflow: ellipsis;
}

	/* 分类标签样式 */
.category.data-v-13e95ff7 {
		padding: 4rpx 12rpx;
		border-radius: 8rpx;
		font-size: 20rpx;
		color: white;
		font-weight: 500;
}
.category-general.data-v-13e95ff7 {
		background: #007aff;
}
.category-registration.data-v-13e95ff7 {
		background: #34c759;
}
.category-carton.data-v-13e95ff7 {
		background: #ff9500;
}
.category-unknown.data-v-13e95ff7 {
		background: #8e8e93;
}

	/* 图片预览 */
.record-image.data-v-13e95ff7 {
		position: relative;
		width: 120rpx;
		height: 120rpx;
		border-radius: 10rpx;
		overflow: hidden;
		flex-shrink: 0;
}
.preview-img.data-v-13e95ff7 {
		width: 100%;
		height: 100%;
}
.preview-overlay.data-v-13e95ff7 {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.3);
		display: flex;
		align-items: center;
		justify-content: center;
		opacity: 0;
		transition: opacity 0.3s;
}
.record-image:active .preview-overlay.data-v-13e95ff7 {
		opacity: 1;
}
.preview-text.data-v-13e95ff7 {
		font-size: 20rpx;
		color: white;
		text-align: center;
}

	/* 无图片状态 */
.no-image.data-v-13e95ff7 {
		width: 120rpx;
		height: 120rpx;
		border-radius: 10rpx;
		background: #f5f5f5;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
}
.no-image-icon.data-v-13e95ff7 {
		font-size: 40rpx;
		margin-bottom: 5rpx;
		opacity: 0.5;
}
.no-image-text.data-v-13e95ff7 {
		font-size: 20rpx;
		color: #999999;
		text-align: center;
}

	/* 底部操作 */
.modal-footer.data-v-13e95ff7 {
		display: flex;
		padding: 20rpx 30rpx;
		border-top: 2rpx solid #f0f0f0;
		background: #fafafa;
		gap: 20rpx;
}
.refresh-btn.data-v-13e95ff7,
	.close-btn-footer.data-v-13e95ff7 {
		flex: 1;
		height: 80rpx;
		border-radius: 15rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		border: none;
}
.refresh-btn.data-v-13e95ff7 {
		background: #007aff;
		color: white;
}
.close-btn-footer.data-v-13e95ff7 {
		background: #f5f5f5;
		color: #333333;
}
.btn-icon.data-v-13e95ff7 {
		margin-right: 8rpx;
}
.btn-text.data-v-13e95ff7 {
		font-size: 28rpx;
}

	/* 滚动条样式优化 */
.records-horizontal-scroll.data-v-13e95ff7::-webkit-scrollbar,
	.records-vertical-scroll.data-v-13e95ff7::-webkit-scrollbar {
		width: 8rpx;
		height: 8rpx;
}
.records-horizontal-scroll.data-v-13e95ff7::-webkit-scrollbar-track,
	.records-vertical-scroll.data-v-13e95ff7::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 4rpx;
}
.records-horizontal-scroll.data-v-13e95ff7::-webkit-scrollbar-thumb,
	.records-vertical-scroll.data-v-13e95ff7::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 4rpx;
}
.records-horizontal-scroll.data-v-13e95ff7::-webkit-scrollbar-thumb:hover,
	.records-vertical-scroll.data-v-13e95ff7::-webkit-scrollbar-thumb:hover {
		background: #a8a8a8;
}

	/* 滚动条角落 */
.records-horizontal-scroll.data-v-13e95ff7::-webkit-scrollbar-corner,
	.records-vertical-scroll.data-v-13e95ff7::-webkit-scrollbar-corner {
		background: #f1f1f1;
}

	/* 移动端滚动条指示器 */
.records-horizontal-scroll.data-v-13e95ff7,
	.records-vertical-scroll.data-v-13e95ff7 {
		scrollbar-width: thin;
		scrollbar-color: #c1c1c1 #f1f1f1;
}
