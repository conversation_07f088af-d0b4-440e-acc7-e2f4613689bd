# shipping_detail表字段修复说明

## 问题描述

在订单查询功能中遇到SQL错误：
```
Unknown column 'updated_at' in 'field list'
```

## 问题原因

代码中引用了 `shipping_detail` 表中不存在的 `updated_at` 字段。

根据实际的表结构，`shipping_detail` 表只有 `created_at` 字段，没有 `updated_at` 字段。

## 实际表结构

```sql
CREATE TABLE `shipping_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `notice_date` date NULL DEFAULT NULL,
  `receiver` varchar(100) NULL DEFAULT NULL,
  `receiver_contact` varchar(100) NULL DEFAULT NULL,
  `deliver_company` varchar(200) NULL DEFAULT NULL,
  `deliver_address` varchar(500) NULL DEFAULT NULL,
  `deliver_contact` varchar(100) NULL DEFAULT NULL,
  `required_arrival_date` date NULL DEFAULT NULL,
  `follower` varchar(50) NULL DEFAULT NULL,
  `shipping_no` varchar(50) NULL DEFAULT NULL,
  `order_no` varchar(50) NOT NULL,
  `product_name` varchar(100) NOT NULL,
  `spec` varchar(200) NULL DEFAULT NULL,
  `width` varchar(50) NULL DEFAULT NULL,
  `weight` varchar(50) NULL DEFAULT NULL,
  `batch_count` varchar(50) NULL DEFAULT NULL,
  `price` varchar(50) NULL DEFAULT NULL,
  `quantity` varchar(50) NULL DEFAULT NULL,
  `remark` longtext NULL,
  `created_at` datetime(6) NOT NULL,
  `dye_price` varchar(50) NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);
```

## 修复内容

### 1. 订单历史查询接口 (GET /api/orders/history)

**修复前：**
```sql
SELECT DISTINCT
    order_no as order_number,
    receiver,
    created_at,
    updated_at  -- ❌ 不存在的字段
FROM shipping_detail
WHERE receiver = ?
ORDER BY updated_at DESC  -- ❌ 不存在的字段
```

**修复后：**
```sql
SELECT DISTINCT
    order_no as order_number,
    receiver,
    created_at  -- ✅ 只查询存在的字段
FROM shipping_detail
WHERE receiver = ?
ORDER BY created_at DESC  -- ✅ 使用存在的字段排序
```

### 2. 订单查询接口 (GET /api/orders/query/:orderNumber)

**修复前：**
```sql
SELECT DISTINCT
    order_no as order_number,
    receiver,
    created_at,
    updated_at  -- ❌ 不存在的字段
FROM shipping_detail
WHERE receiver = ? AND order_no LIKE ?
ORDER BY ... updated_at DESC  -- ❌ 不存在的字段
```

**修复后：**
```sql
SELECT DISTINCT
    order_no as order_number,
    receiver,
    created_at  -- ✅ 只查询存在的字段
FROM shipping_detail
WHERE receiver = ? AND order_no LIKE ?
ORDER BY ... created_at DESC  -- ✅ 使用存在的字段排序
```

### 3. 数据处理逻辑修复

**修复前：**
```javascript
return {
    ...order,
    image_count: stats[0]?.image_count || 0,
    created_date: order.created_at,
    latest_upload: order.updated_at  // ❌ 引用不存在的字段
};
```

**修复后：**
```javascript
return {
    ...order,
    image_count: stats[0]?.image_count || 0,
    created_date: order.created_at,
    latest_upload: order.created_at  // ✅ 使用存在的字段
};
```

## 测试验证

### 1. 启动后端服务
```bash
cd server
npm run dev:local:watch
```

### 2. 测试订单查询
```bash
# 测试查询接口
curl -X GET "http://localhost:3002/api/orders/query/J250560" \
  -H "Authorization: Bearer your_token"

# 测试历史接口
curl -X GET "http://localhost:3002/api/orders/history" \
  -H "Authorization: Bearer your_token"
```

## 注意事项

1. **字段一致性**：确保代码中引用的字段与数据库表结构一致
2. **时间字段**：由于只有 `created_at` 字段，所有时间相关的显示都使用这个字段
3. **排序逻辑**：按 `created_at` 降序排列，显示最新创建的订单
4. **兼容性**：前端显示逻辑保持不变，只是数据源字段调整

## 影响范围

- ✅ 订单历史查询功能恢复正常
- ✅ 订单模糊查询功能恢复正常
- ✅ 前端界面显示正常
- ✅ 权限控制功能正常

## 后续建议

1. **数据库设计**：如果需要记录更新时间，可以考虑在 `shipping_detail` 表中添加 `updated_at` 字段
2. **代码审查**：在修改数据源时，应先确认目标表的字段结构
3. **测试覆盖**：增加数据库字段变更的测试用例
