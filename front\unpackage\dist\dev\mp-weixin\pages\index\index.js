"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_apiService = require("../../utils/apiService.js");
const utils_helpers = require("../../utils/helpers.js");
const utils_warehouseUsers = require("../../utils/warehouseUsers.js");
const ImagePicker = () => "../../components/ImagePicker.js";
const HistoryModal = () => "../../components/HistoryModal.js";
const RegistrationFormModal = () => "../../components/RegistrationFormModal.js";
const CartonInfoModal = () => "../../components/CartonInfoModal.js";
const _sfc_main = {
  components: {
    ImagePicker,
    HistoryModal,
    RegistrationFormModal,
    CartonInfoModal
  },
  data() {
    return {
      selectedImages: [],
      isRecognizing: false,
      recognitionStatusText: "正在识别中，请稍候...",
      currentImageIndex: 0,
      totalImages: 0,
      showHistoryModal: false,
      showConfirmModal: false,
      registrationData: [],
      showCartonConfirmModal: false,
      cartonInfoData: {},
      currentImageInfo: {},
      currentUser: null
      // 当前登录用户
    };
  },
  computed: {
    progressPercentage() {
      if (this.totalImages === 0)
        return 0;
      return Math.round(this.currentImageIndex / this.totalImages * 100);
    }
  },
  onLoad() {
    common_vendor.index.__f__("log", "at pages/index/index.vue:117", "📦 仓库管理页面加载");
    if (!utils_warehouseUsers.warehouseUserManager.checkLoginStatus()) {
      common_vendor.index.__f__("log", "at pages/index/index.vue:121", "❌ 用户未登录，跳转到登录页面");
      common_vendor.index.redirectTo({
        url: "/pages/warehouseLogin/warehouseLogin",
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/index/index.vue:125", "跳转到登录页面失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "error"
          });
        }
      });
      return;
    }
    this.currentUser = utils_warehouseUsers.warehouseUserManager.getCurrentUser();
    common_vendor.index.__f__("log", "at pages/index/index.vue:137", "✅ 用户已登录:", this.currentUser.displayName);
  },
  methods: {
    /**
     * 处理用户登出
     */
    handleLogout() {
      common_vendor.index.showModal({
        title: "确认登出",
        content: "确定要退出仓库管理系统吗？",
        success: (res) => {
          if (res.confirm) {
            const success = utils_warehouseUsers.warehouseUserManager.logout();
            if (success) {
              common_vendor.index.showToast({
                title: "已退出登录",
                icon: "success",
                duration: 1500
              });
              setTimeout(() => {
                common_vendor.index.redirectTo({
                  url: "/pages/warehouseLogin/warehouseLogin"
                });
              }, 1500);
            } else {
              common_vendor.index.showToast({
                title: "退出失败",
                icon: "error"
              });
            }
          }
        }
      });
    },
    /**
     * 图片选择变化事件 - 自动开始识别
     */
    async onImagesChange(images) {
      common_vendor.index.__f__("log", "at pages/index/index.vue:178", "📸 图片选择变化:", images);
      if (images && images.length > 0) {
        if (images.length === 1) {
          await this.recognizeSingleImage(images[0]);
        } else {
          await this.recognizeMultipleImages(images);
        }
      }
    },
    /**
     * 识别单张图片（支持表格识别）
     */
    async recognizeSingleImage(imagePath) {
      this.isRecognizing = true;
      this.recognitionStatusText = "正在识别图片内容...";
      try {
        const isConnected = await utils_apiService.apiService.checkServerConnection();
        if (!isConnected) {
          utils_helpers.showError("无法连接到服务器，请检查网络连接");
          this.isRecognizing = false;
          return;
        }
        this.currentImageInfo = {
          imagePath,
          imageName: "选中的图片",
          uploadDate: (/* @__PURE__ */ new Date()).toISOString()
        };
        const registrationResult = await this.tryRegistrationFormRecognize(imagePath);
        if (registrationResult.success) {
          this.isRecognizing = false;
          return;
        }
        const cartonResult = await this.tryCartonInfoRecognize(imagePath);
        if (cartonResult.success) {
          this.isRecognizing = false;
          return;
        }
        await this.performGeneralRecognition(imagePath);
      } catch (error) {
        utils_helpers.showError("识别失败，请重试");
      } finally {
        this.isRecognizing = false;
        this.recognitionStatusText = "正在识别中，请稍候...";
        this.selectedImages = [];
      }
    },
    /**
     * 识别多张图片
     */
    async recognizeMultipleImages(images) {
      if (!images || images.length === 0) {
        utils_helpers.showError("请先选择图片");
        return;
      }
      this.isRecognizing = true;
      this.totalImages = images.length;
      this.currentImageIndex = 0;
      common_vendor.index.__f__("log", "at pages/index/index.vue:253", `🚀 开始批量识别 ${images.length} 张图片`);
      try {
        const isConnected = await utils_apiService.apiService.checkServerConnection();
        if (!isConnected) {
          utils_helpers.showError("无法连接到服务器，请检查网络连接");
          this.isRecognizing = false;
          return;
        }
        let successCount = 0;
        let failCount = 0;
        for (let i = 0; i < images.length; i++) {
          this.currentImageIndex = i + 1;
          const imagePath = images[i];
          this.recognitionStatusText = `正在识别第 ${i + 1} 张图片...`;
          common_vendor.index.__f__("log", "at pages/index/index.vue:273", `📷 处理第 ${i + 1}/${images.length} 张图片:`, imagePath);
          try {
            const currentUser = utils_warehouseUsers.warehouseUserManager.getCurrentUser();
            const username = currentUser ? currentUser.username : null;
            await utils_apiService.apiService.recognizeImage(imagePath, username);
            common_vendor.index.__f__("log", "at pages/index/index.vue:282", `✅ 第 ${i + 1} 张图片识别成功`);
            successCount++;
          } catch (error) {
            common_vendor.index.__f__("error", "at pages/index/index.vue:285", `❌ 第 ${i + 1} 张图片识别失败:`, error.message);
            failCount++;
          }
          if (i < images.length - 1) {
            await new Promise((resolve) => setTimeout(resolve, 500));
          }
        }
        if (successCount === images.length) {
          utils_helpers.showSuccess(`🎉 全部 ${images.length} 张图片识别完成并已保存`);
        } else if (successCount > 0) {
          utils_helpers.showSuccess(`✅ ${successCount} 张图片识别成功，${failCount} 张失败`);
        } else {
          utils_helpers.showError(`❌ 所有图片识别失败，请检查图片质量或网络连接`);
        }
        this.selectedImages = [];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:308", "批量识别过程出错:", error);
        utils_helpers.showError("批量识别失败，请重试");
      } finally {
        this.isRecognizing = false;
        this.currentImageIndex = 0;
        this.totalImages = 0;
        this.recognitionStatusText = "正在识别中，请稍候...";
      }
    },
    /**
     * 尝试装柜放船样登记表识别
     */
    async tryRegistrationFormRecognize(imagePath) {
      try {
        const result = await utils_apiService.apiService.recognizeRegistrationForm(imagePath);
        if (result.isRegistrationForm) {
          this.registrationData = result.data.extractedData;
          this.currentImageInfo = result.data.imageInfo;
          this.showConfirmModal = true;
          utils_helpers.showSuccess("检测到装柜放船样登记表");
          return { success: true };
        }
        return { success: false };
      } catch (error) {
        return { success: false };
      }
    },
    /**
     * 尝试装柜信息表识别
     */
    async tryCartonInfoRecognize(imagePath) {
      try {
        const result = await utils_apiService.apiService.recognizeCartonInfo(imagePath);
        if (result.isCartonInfo) {
          this.cartonInfoData = result.data.extractedData;
          this.currentImageInfo = result.data.imageInfo;
          this.showCartonConfirmModal = true;
          utils_helpers.showSuccess("检测到装柜信息表");
          return { success: true };
        }
        return { success: false };
      } catch (error) {
        return { success: false };
      }
    },
    /**
     * 执行通用识别
     */
    async performGeneralRecognition(imagePath) {
      try {
        const currentUser = utils_warehouseUsers.warehouseUserManager.getCurrentUser();
        const username = currentUser ? currentUser.username : null;
        const result = await utils_apiService.apiService.recognizeImage(imagePath, username);
        utils_helpers.showSuccess(result.message || "文字识别完成并已保存到数据库");
      } catch (error) {
        throw error;
      }
    },
    /**
     * 历史记录相关方法 - 暂时隐藏
     */
    // toggleHistory() {
    // 	this.showHistory = !this.showHistory;
    // },
    // closeHistory() {
    // 	this.showHistory = false;
    // },
    /**
     * 装柜放船样登记表确认事件
     */
    onRegistrationConfirm() {
      this.showConfirmModal = false;
      utils_helpers.showSuccess("装柜放船样登记表数据保存成功！");
    },
    onRegistrationCancel() {
      this.showConfirmModal = false;
    },
    /**
     * 装柜信息表确认事件
     */
    onCartonInfoConfirm() {
      this.showCartonConfirmModal = false;
      utils_helpers.showSuccess("装柜信息表数据保存成功！");
    },
    onCartonInfoCancel() {
      this.showCartonConfirmModal = false;
    },
    /**
     * 显示今日历史记录
     */
    showHistory() {
      common_vendor.index.__f__("log", "at pages/index/index.vue:413", "📋 打开今日历史记录");
      this.showHistoryModal = true;
    },
    /**
     * 关闭历史记录弹窗
     */
    closeHistory() {
      common_vendor.index.__f__("log", "at pages/index/index.vue:421", "📋 关闭今日历史记录");
      this.showHistoryModal = false;
    }
  }
};
if (!Array) {
  const _component_ImagePicker = common_vendor.resolveComponent("ImagePicker");
  const _component_HistoryModal = common_vendor.resolveComponent("HistoryModal");
  const _component_RegistrationFormModal = common_vendor.resolveComponent("RegistrationFormModal");
  const _component_CartonInfoModal = common_vendor.resolveComponent("CartonInfoModal");
  (_component_ImagePicker + _component_HistoryModal + _component_RegistrationFormModal + _component_CartonInfoModal)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.currentUser
  }, $data.currentUser ? {
    b: common_vendor.t($data.currentUser.displayName),
    c: common_vendor.t($data.currentUser.department),
    d: common_vendor.o((...args) => $options.handleLogout && $options.handleLogout(...args))
  } : {}, {
    e: common_vendor.o($options.onImagesChange),
    f: common_vendor.o(($event) => $data.selectedImages = $event),
    g: common_vendor.p({
      modelValue: $data.selectedImages
    }),
    h: $data.isRecognizing
  }, $data.isRecognizing ? common_vendor.e({
    i: common_vendor.t($data.recognitionStatusText),
    j: $data.totalImages > 1
  }, $data.totalImages > 1 ? {
    k: common_vendor.t($data.currentImageIndex),
    l: common_vendor.t($data.totalImages),
    m: $options.progressPercentage + "%"
  } : {}) : {}, {
    n: $data.isRecognizing
  }, $data.isRecognizing ? {} : {}, {
    o: common_vendor.o((...args) => $options.showHistory && $options.showHistory(...args)),
    p: common_vendor.o($options.closeHistory),
    q: common_vendor.p({
      visible: $data.showHistoryModal
    }),
    r: common_vendor.o($options.onRegistrationConfirm),
    s: common_vendor.o($options.onRegistrationCancel),
    t: common_vendor.p({
      visible: $data.showConfirmModal,
      data: $data.registrationData,
      imageInfo: $data.currentImageInfo
    }),
    v: common_vendor.o($options.onCartonInfoConfirm),
    w: common_vendor.o($options.onCartonInfoCancel),
    x: common_vendor.p({
      visible: $data.showCartonConfirmModal,
      data: $data.cartonInfoData,
      imageInfo: $data.currentImageInfo
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1cf27b2a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
