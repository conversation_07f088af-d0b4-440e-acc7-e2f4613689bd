/**
 * 订单管理路由
 * 处理订单相关的API请求
 */

const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const { verifyToken } = require('./auth');

/**
 * 获取订单历史列表
 * GET /api/orders/history
 */
router.get('/history', verifyToken, async (req, res) => {
    try {
        // 从token中获取用户信息
        const userId = req.user.id;
        
        // 先获取用户的工厂名称
        const userSql = `
            SELECT factory_name 
            FROM Factory_Login 
            WHERE id = ? AND status = 1
        `;
        
        const users = await query(userSql, [userId]);
        
        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }
        
        const factoryName = users[0].factory_name;
        
        // 基于shipping_detail表查询该工厂的订单历史
        const orderSql = `
            SELECT DISTINCT
                order_no as order_number,
                receiver,
                created_at
            FROM shipping_detail
            WHERE receiver = ?
            ORDER BY created_at DESC
            LIMIT 50
        `;

        const orders = await query(orderSql, [factoryName]);

        // 为每个订单添加图片统计信息
        const ordersWithStats = await Promise.all(orders.map(async (order) => {
            try {
                const statsSql = `
                    SELECT COUNT(*) as image_count
                    FROM Img_Info
                    WHERE factory_name = ? AND order_number = ?
                `;
                const stats = await query(statsSql, [factoryName, order.order_number]);
                return {
                    ...order,
                    image_count: stats[0]?.image_count || 0,
                    created_date: order.created_at,
                    latest_upload: order.created_at
                };
            } catch (error) {
                console.error('获取订单图片统计失败:', error);
                return {
                    ...order,
                    image_count: 0,
                    created_date: order.created_at,
                    latest_upload: order.created_at
                };
            }
        }));
        
        res.json({
            success: true,
            message: '获取订单历史成功',
            data: ordersWithStats
        });

        console.log(`📋 获取订单历史: ${factoryName}, 共 ${ordersWithStats.length} 个订单`);

    } catch (error) {
        console.error('❌ 获取订单历史失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 查询订单信息（基于shipping_detail表，支持模糊查询）
 * GET /api/orders/query/:orderNumber
 */
router.get('/query/:orderNumber', verifyToken, async (req, res) => {
    try {
        const { orderNumber } = req.params;
        const userId = req.user.id;

        if (!orderNumber || orderNumber.trim() === '') {
            return res.status(400).json({
                success: false,
                message: '订单号不能为空'
            });
        }

        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name
            FROM Factory_Login
            WHERE id = ? AND status = 1
        `;

        const users = await query(userSql, [userId]);

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const factoryName = users[0].factory_name;
        const searchTerm = orderNumber.trim();

        // 基于shipping_detail表进行模糊查询
        const orderSql = `
            SELECT DISTINCT
                order_no as order_number,
                receiver,
                created_at
            FROM shipping_detail
            WHERE receiver = ? AND order_no LIKE ?
            ORDER BY
                CASE
                    WHEN order_no = ? THEN 1
                    WHEN order_no LIKE ? THEN 2
                    ELSE 3
                END,
                created_at DESC
            LIMIT 10
        `;

        // 构建查询参数
        const searchPattern = `%${searchTerm}%`;
        const startPattern = `${searchTerm}%`;
        const queryParams = [factoryName, searchPattern, searchTerm, startPattern];

        const orders = await query(orderSql, queryParams);

        if (orders.length === 0) {
            return res.status(404).json({
                success: false,
                message: '未找到匹配的订单'
            });
        }

        // 为每个订单添加图片统计信息（如果需要的话）
        const ordersWithStats = await Promise.all(orders.map(async (order) => {
            try {
                const statsSql = `
                    SELECT COUNT(*) as image_count
                    FROM Img_Info
                    WHERE factory_name = ? AND order_number = ?
                `;
                const stats = await query(statsSql, [factoryName, order.order_number]);
                return {
                    ...order,
                    image_count: stats[0]?.image_count || 0
                };
            } catch (error) {
                console.error('获取订单图片统计失败:', error);
                return {
                    ...order,
                    image_count: 0
                };
            }
        }));

        // 如果只有一个结果，返回单个订单；如果有多个结果，返回列表
        const result = ordersWithStats.length === 1 ? ordersWithStats[0] : ordersWithStats;

        res.json({
            success: true,
            message: ordersWithStats.length === 1 ? '查询订单成功' : `找到 ${ordersWithStats.length} 个匹配的订单`,
            data: result,
            count: ordersWithStats.length,
            searchTerm: searchTerm
        });

        console.log(`🔍 模糊查询订单: "${searchTerm}" (${factoryName}) - 找到 ${ordersWithStats.length} 个结果`);

    } catch (error) {
        console.error('❌ 查询订单失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取指定订单的发货单号列表
 * GET /api/orders/:orderNumber/shipping-numbers
 */
router.get('/:orderNumber/shipping-numbers', verifyToken, async (req, res) => {
    try {
        const { orderNumber } = req.params;
        const userId = req.user.id;

        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name
            FROM Factory_Login
            WHERE id = ? AND status = 1
        `;

        const users = await query(userSql, [userId]);

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const factoryName = users[0].factory_name;

        // 查询该订单对应的发货单号列表
        const shippingSql = `
            SELECT DISTINCT shipping_no
            FROM shipping_detail
            WHERE receiver = ? AND order_no = ? AND shipping_no IS NOT NULL AND shipping_no != ''
            ORDER BY shipping_no
        `;

        const shippingNumbers = await query(shippingSql, [factoryName, orderNumber]);

        // 如果没有发货单号，返回默认值
        const result = shippingNumbers.length > 0
            ? shippingNumbers.map(row => row.shipping_no)
            : ['DEFAULT'];

        res.json({
            success: true,
            message: '获取发货单号列表成功',
            data: result
        });

        console.log(`📦 获取订单发货单号: ${orderNumber}, 共 ${result.length} 个发货单号`);

    } catch (error) {
        console.error('❌ 获取发货单号失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取指定订单的发货详细信息
 * GET /api/orders/:orderNumber/shipping-details?shipping_number=xxx
 */
router.get('/:orderNumber/shipping-details', verifyToken, async (req, res) => {
    try {
        const { orderNumber } = req.params;
        const { shipping_number } = req.query;
        const userId = req.user.id;

        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name
            FROM Factory_Login
            WHERE id = ? AND status = 1
        `;

        const users = await query(userSql, [userId]);

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const factoryName = users[0].factory_name;

        // 构建查询条件
        let whereCondition = 'WHERE receiver = ? AND order_no = ?';
        let queryParams = [factoryName, orderNumber];

        if (shipping_number && shipping_number !== 'ALL') {
            whereCondition += ' AND shipping_no = ?';
            queryParams.push(shipping_number);
        }

        // 查询该订单的发货详细信息
        const detailSql = `
            SELECT
                shipping_no,
                notice_date,
                deliver_company,
                follower,
                product_name,
                spec,
                width,
                weight,
                quantity,
                remark,
                IFNULL(length, NULL) as length,
                IFNULL(weight_total, NULL) as weight_total,
                IFNULL(actual_price, NULL) as actual_price,
                created_at
            FROM shipping_detail
            ${whereCondition}
            ORDER BY notice_date DESC, shipping_no
        `;

        const shippingDetails = await query(detailSql, queryParams);

        if (shippingDetails.length === 0) {
            return res.status(404).json({
                success: false,
                message: '未找到该订单的发货信息'
            });
        }

        res.json({
            success: true,
            message: '获取发货详细信息成功',
            data: shippingDetails
        });

        const filterInfo = shipping_number && shipping_number !== 'ALL' ? ` (发货单号: ${shipping_number})` : '';
        console.log(`📋 获取订单发货详情: ${orderNumber}${filterInfo}, 共 ${shippingDetails.length} 条记录`);

    } catch (error) {
        console.error('❌ 获取发货详细信息失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取指定订单的图片列表
 * GET /api/orders/:orderNumber/images
 */
router.get('/:orderNumber/images', verifyToken, async (req, res) => {
    try {
        const { orderNumber } = req.params;
        const userId = req.user.id;

        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name
            FROM Factory_Login
            WHERE id = ? AND status = 1
        `;

        const users = await query(userSql, [userId]);

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const factoryName = users[0].factory_name;

        // 查询订单的图片列表
        const imagesSql = `
            SELECT
                id,
                image_name,
                image_path,
                file_size,
                upload_date
            FROM Img_Info
            WHERE factory_name = ? AND order_number = ?
            ORDER BY upload_date DESC
        `;

        const images = await query(imagesSql, [factoryName, orderNumber]);

        res.json({
            success: true,
            message: '获取图片列表成功',
            data: images
        });

        console.log(`🖼️ 获取订单图片: ${orderNumber}, 共 ${images.length} 张图片`);

    } catch (error) {
        console.error('❌ 获取订单图片失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});



/**
 * 获取订单统计信息
 * GET /api/orders/stats
 */
router.get('/stats', verifyToken, async (req, res) => {
    try {
        const userId = req.user.id;
        
        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name 
            FROM Factory_Login 
            WHERE id = ? AND status = 1
        `;
        
        const users = await query(userSql, [userId]);
        
        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }
        
        const factoryName = users[0].factory_name;
        
        // 获取统计信息
        const statsSql = `
            SELECT 
                COUNT(DISTINCT order_number) as total_orders,
                COUNT(*) as total_images,
                SUM(file_size) as total_size,
                DATE(MAX(upload_date)) as last_upload_date
            FROM Img_Info 
            WHERE factory_name = ?
        `;
        
        const stats = await query(statsSql, [factoryName]);
        
        // 获取今日统计
        const todayStatsSql = `
            SELECT 
                COUNT(DISTINCT order_number) as today_orders,
                COUNT(*) as today_images
            FROM Img_Info 
            WHERE factory_name = ? AND DATE(upload_date) = CURDATE()
        `;
        
        const todayStats = await query(todayStatsSql, [factoryName]);
        
        const result = {
            ...stats[0],
            ...todayStats[0]
        };
        
        res.json({
            success: true,
            message: '获取统计信息成功',
            data: result
        });
        
        console.log(`📊 获取订单统计: ${factoryName}`);

    } catch (error) {
        console.error('❌ 获取订单统计失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 新增发货信息
 * POST /api/orders/add-shipping-info
 */
router.post('/add-shipping-info', verifyToken, async (req, res) => {
    try {
        const { orderNumber, shippingNumber, length, weight_total, actual_price } = req.body;
        const userId = req.user.id;

        // 验证必填字段（只验证订单号和发货单号）
        if (!orderNumber || !shippingNumber) {
            return res.status(400).json({
                success: false,
                message: '订单号和发货单号不能为空'
            });
        }

        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name
            FROM Factory_Login
            WHERE id = ? AND status = 1
        `;

        const users = await query(userSql, [userId]);

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const factoryName = users[0].factory_name;

        // 首先检查表结构，确保字段存在
        try {
            const checkColumnsSQL = `
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = 'identify'
                AND TABLE_NAME = 'shipping_detail'
                AND COLUMN_NAME IN ('length', 'weight_total', 'actual_price')
            `;

            const columns = await query(checkColumnsSQL);
            const existingColumns = columns.map(col => col.COLUMN_NAME);

            console.log('📊 shipping_detail表中存在的字段:', existingColumns);

            // 检查是否已存在相同的发货记录
            const checkSql = `
                SELECT id FROM shipping_detail
                WHERE receiver = ? AND order_no = ? AND shipping_no = ?
            `;

            const existingRecords = await query(checkSql, [factoryName, orderNumber, shippingNumber]);

            if (existingRecords.length > 0) {
                // 如果存在，更新记录 - 只更新存在的字段
                let updateFields = [];
                let updateValues = [];

                if (existingColumns.includes('length') && length !== null && length !== undefined) {
                    updateFields.push('length = ?');
                    updateValues.push(length);
                }
                if (existingColumns.includes('weight_total') && weight_total !== null && weight_total !== undefined) {
                    updateFields.push('weight_total = ?');
                    updateValues.push(weight_total);
                }
                if (existingColumns.includes('actual_price') && actual_price !== null && actual_price !== undefined) {
                    updateFields.push('actual_price = ?');
                    updateValues.push(actual_price);
                }

                if (updateFields.length > 0) {
                    const updateSql = `
                        UPDATE shipping_detail
                        SET ${updateFields.join(', ')}
                        WHERE receiver = ? AND order_no = ? AND shipping_no = ?
                    `;

                    updateValues.push(factoryName, orderNumber, shippingNumber);
                    await query(updateSql, updateValues);

                    console.log(`📝 更新发货信息: ${orderNumber} - ${shippingNumber}`);
                } else {
                    console.log(`⚠️ 没有可更新的字段: ${orderNumber} - ${shippingNumber}`);
                }
            } else {
                // 如果不存在，插入新记录 - 只插入存在的字段
                let insertFields = ['receiver', 'order_no', 'shipping_no', 'notice_date', 'created_at'];
                let insertPlaceholders = ['?', '?', '?', 'CURDATE()', 'NOW()'];
                let insertValues = [factoryName, orderNumber, shippingNumber];

                if (existingColumns.includes('length') && length !== null && length !== undefined) {
                    insertFields.push('length');
                    insertPlaceholders.push('?');
                    insertValues.push(length);
                }
                if (existingColumns.includes('weight_total') && weight_total !== null && weight_total !== undefined) {
                    insertFields.push('weight_total');
                    insertPlaceholders.push('?');
                    insertValues.push(weight_total);
                }
                if (existingColumns.includes('actual_price') && actual_price !== null && actual_price !== undefined) {
                    insertFields.push('actual_price');
                    insertPlaceholders.push('?');
                    insertValues.push(actual_price);
                }

                const insertSql = `
                    INSERT INTO shipping_detail (${insertFields.join(', ')})
                    VALUES (${insertPlaceholders.join(', ')})
                `;

                await query(insertSql, insertValues);

                console.log(`➕ 新增发货信息: ${orderNumber} - ${shippingNumber}`);
            }
        } catch (columnCheckError) {
            console.error('❌ 检查表结构失败:', columnCheckError);
            throw new Error('数据库表结构检查失败');
        }

        res.json({
            success: true,
            message: '发货信息保存成功'
        });

    } catch (error) {
        console.error('❌ 保存发货信息失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

module.exports = router;
