"use strict";
const common_vendor = require("../common/vendor.js");
class DataCacheManager {
  constructor() {
    this.memoryCache = /* @__PURE__ */ new Map();
    this.maxMemoryCacheSize = 50;
    this.storagePrefix = "data_cache_";
    this.defaultExpireTime = 5 * 60 * 1e3;
    this.expireTimeConfig = {
      "images": 2 * 60 * 1e3,
      // 图片列表：2分钟
      "orders": 5 * 60 * 1e3,
      // 订单列表：5分钟
      "userInfo": 30 * 60 * 1e3
      // 用户信息：30分钟
    };
  }
  /**
   * 生成缓存键
   * @param {string} type 数据类型
   * @param {string} key 数据键
   * @returns {string} 缓存键
   */
  generateCacheKey(type, key) {
    return `${type}_${key}`;
  }
  /**
   * 获取过期时间
   * @param {string} type 数据类型
   * @returns {number} 过期时间（毫秒）
   */
  getExpireTime(type) {
    return this.expireTimeConfig[type] || this.defaultExpireTime;
  }
  /**
   * 从内存缓存获取数据
   * @param {string} type 数据类型
   * @param {string} key 数据键
   * @returns {any|null} 缓存的数据或null
   */
  getFromMemoryCache(type, key) {
    const cacheKey = this.generateCacheKey(type, key);
    const cached = this.memoryCache.get(cacheKey);
    if (cached) {
      const expireTime = this.getExpireTime(type);
      if (Date.now() - cached.timestamp < expireTime) {
        cached.lastAccess = Date.now();
        return cached.data;
      } else {
        this.memoryCache.delete(cacheKey);
      }
    }
    return null;
  }
  /**
   * 存储到内存缓存
   * @param {string} type 数据类型
   * @param {string} key 数据键
   * @param {any} data 数据
   */
  setToMemoryCache(type, key, data) {
    const cacheKey = this.generateCacheKey(type, key);
    const now = Date.now();
    if (this.memoryCache.size >= this.maxMemoryCacheSize) {
      this.cleanupMemoryCache();
    }
    this.memoryCache.set(cacheKey, {
      data,
      timestamp: now,
      lastAccess: now,
      type,
      key
    });
  }
  /**
   * 清理内存缓存（LRU策略）
   */
  cleanupMemoryCache() {
    const entries = Array.from(this.memoryCache.entries());
    entries.sort((a, b) => a[1].lastAccess - b[1].lastAccess);
    const deleteCount = Math.floor(this.maxMemoryCacheSize * 0.25);
    for (let i = 0; i < deleteCount && entries.length > 0; i++) {
      this.memoryCache.delete(entries[i][0]);
    }
  }
  /**
   * 从本地存储获取数据
   * @param {string} type 数据类型
   * @param {string} key 数据键
   * @returns {any|null} 缓存的数据或null
   */
  getFromStorageCache(type, key) {
    try {
      const cacheKey = this.storagePrefix + this.generateCacheKey(type, key);
      const cached = common_vendor.index.getStorageSync(cacheKey);
      if (cached) {
        const expireTime = this.getExpireTime(type);
        if (Date.now() - cached.timestamp < expireTime) {
          return cached.data;
        } else {
          common_vendor.index.removeStorageSync(cacheKey);
        }
      }
    } catch (error) {
      common_vendor.index.__f__("warn", "at utils/dataCache.js:130", "从本地存储获取数据缓存失败:", error);
    }
    return null;
  }
  /**
   * 存储到本地缓存
   * @param {string} type 数据类型
   * @param {string} key 数据键
   * @param {any} data 数据
   */
  setToStorageCache(type, key, data) {
    try {
      const cacheKey = this.storagePrefix + this.generateCacheKey(type, key);
      const cacheData = {
        data,
        timestamp: Date.now(),
        type,
        key
      };
      common_vendor.index.setStorageSync(cacheKey, cacheData);
    } catch (error) {
      common_vendor.index.__f__("warn", "at utils/dataCache.js:154", "存储到本地缓存失败:", error);
    }
  }
  /**
   * 获取数据（优先从缓存获取）
   * @param {string} type 数据类型
   * @param {string} key 数据键
   * @param {Function} fetchFunction 获取数据的函数
   * @param {Object} options 选项
   * @returns {Promise<any>} 数据
   */
  async getData(type, key, fetchFunction, options = {}) {
    const { forceRefresh = false, useStorage = true } = options;
    if (!forceRefresh) {
      const memoryCache = this.getFromMemoryCache(type, key);
      if (memoryCache !== null) {
        return memoryCache;
      }
      if (useStorage) {
        const storageCache = this.getFromStorageCache(type, key);
        if (storageCache !== null) {
          this.setToMemoryCache(type, key, storageCache);
          return storageCache;
        }
      }
    }
    try {
      const data = await fetchFunction();
      this.setToMemoryCache(type, key, data);
      if (useStorage) {
        this.setToStorageCache(type, key, data);
      }
      return data;
    } catch (error) {
      const expiredCache = this.getExpiredCache(type, key);
      if (expiredCache !== null) {
        common_vendor.index.__f__("warn", "at utils/dataCache.js:203", "网络请求失败，返回过期缓存数据:", error);
        return expiredCache;
      }
      throw error;
    }
  }
  /**
   * 获取过期的缓存数据（用于网络失败时的降级）
   * @param {string} type 数据类型
   * @param {string} key 数据键
   * @returns {any|null} 过期的缓存数据或null
   */
  getExpiredCache(type, key) {
    try {
      const cacheKey = this.storagePrefix + this.generateCacheKey(type, key);
      const cached = common_vendor.index.getStorageSync(cacheKey);
      return cached ? cached.data : null;
    } catch (error) {
      return null;
    }
  }
  /**
   * 删除指定缓存
   * @param {string} type 数据类型
   * @param {string} key 数据键
   */
  removeCache(type, key) {
    const cacheKey = this.generateCacheKey(type, key);
    this.memoryCache.delete(cacheKey);
    try {
      const storageCacheKey = this.storagePrefix + cacheKey;
      common_vendor.index.removeStorageSync(storageCacheKey);
    } catch (error) {
      common_vendor.index.__f__("warn", "at utils/dataCache.js:243", "删除本地存储缓存失败:", error);
    }
  }
  /**
   * 删除指定类型的所有缓存
   * @param {string} type 数据类型
   */
  removeCacheByType(type) {
    const memoryKeys = Array.from(this.memoryCache.keys());
    for (const key of memoryKeys) {
      if (key.startsWith(type + "_")) {
        this.memoryCache.delete(key);
      }
    }
    try {
      const info = common_vendor.index.getStorageInfoSync();
      const storageKeys = info.keys.filter(
        (key) => key.startsWith(this.storagePrefix + type + "_")
      );
      for (const key of storageKeys) {
        common_vendor.index.removeStorageSync(key);
      }
    } catch (error) {
      common_vendor.index.__f__("warn", "at utils/dataCache.js:271", "删除本地存储缓存失败:", error);
    }
  }
  /**
   * 清除所有缓存
   */
  clearAllCache() {
    this.memoryCache.clear();
    try {
      const info = common_vendor.index.getStorageInfoSync();
      const cacheKeys = info.keys.filter((key) => key.startsWith(this.storagePrefix));
      for (const key of cacheKeys) {
        common_vendor.index.removeStorageSync(key);
      }
    } catch (error) {
      common_vendor.index.__f__("warn", "at utils/dataCache.js:291", "清除本地存储缓存失败:", error);
    }
  }
  /**
   * 预加载数据
   * @param {string} type 数据类型
   * @param {string} key 数据键
   * @param {Function} fetchFunction 获取数据的函数
   */
  async preloadData(type, key, fetchFunction) {
    const cached = this.getFromMemoryCache(type, key) || this.getFromStorageCache(type, key);
    if (cached !== null) {
      return;
    }
    try {
      const data = await fetchFunction();
      this.setToMemoryCache(type, key, data);
      this.setToStorageCache(type, key, data);
    } catch (error) {
      common_vendor.index.__f__("warn", "at utils/dataCache.js:314", "预加载数据失败:", error);
    }
  }
  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计信息
   */
  getCacheStats() {
    const memorySize = this.memoryCache.size;
    let storageSize = 0;
    let storageSizeByType = {};
    try {
      const info = common_vendor.index.getStorageInfoSync();
      const cacheKeys = info.keys.filter((key) => key.startsWith(this.storagePrefix));
      storageSize = cacheKeys.length;
      for (const key of cacheKeys) {
        const typeMatch = key.match(new RegExp(`^${this.storagePrefix}([^_]+)_`));
        if (typeMatch) {
          const type = typeMatch[1];
          storageSizeByType[type] = (storageSizeByType[type] || 0) + 1;
        }
      }
    } catch (error) {
      common_vendor.index.__f__("warn", "at utils/dataCache.js:342", "获取存储缓存统计失败:", error);
    }
    return {
      memoryCache: {
        size: memorySize,
        maxSize: this.maxMemoryCacheSize
      },
      storageCache: {
        size: storageSize,
        byType: storageSizeByType
      },
      expireTimeConfig: this.expireTimeConfig
    };
  }
}
const dataCacheManager = new DataCacheManager();
exports.dataCacheManager = dataCacheManager;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/dataCache.js.map
