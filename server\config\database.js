/**
 * 数据库配置文件
 * 优化版本 - 支持高并发连接池和监控
 */
const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
    host: process.env.DB_HOST || '************',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'mls01',
    password: process.env.DB_PASSWORD || '12345@Mls',
    database: process.env.DB_NAME || 'identify',
    charset: 'utf8mb4',
    timezone: '+08:00'
};

// 高并发连接池配置
const poolConfig = {
    ...dbConfig,
    // 连接池配置
    connectionLimit: 100,              // 最大连接数：100个连接
    acquireTimeout: 60000,             // 获取连接超时时间：60秒（mysql2支持）

    // 队列配置
    queueLimit: 200,                   // 等待队列最大长度：200个请求
    waitForConnections: true,          // 等待可用连接

    // 连接管理
    idleTimeout: 300000,               // 空闲连接超时：5分钟
    maxIdle: 20,                       // 最大空闲连接数：20个

    // 性能优化
    enableKeepAlive: true,             // 启用TCP Keep-Alive
    keepAliveInitialDelay: 0,          // Keep-Alive初始延迟

    // 安全配置
    ssl: false,                        // 根据需要启用SSL
    multipleStatements: false,         // 禁用多语句查询（安全考虑）

    // 调试配置
    debug: false
};

// 创建连接池
const pool = mysql.createPool(poolConfig);

// 连接池监控统计
let poolStats = {
    totalConnections: 0,
    activeConnections: 0,
    idleConnections: 0,
    queuedRequests: 0,
    totalQueries: 0,
    failedQueries: 0,
    avgQueryTime: 0,
    lastResetTime: new Date()
};

// 连接池事件监听
pool.on('connection', (connection) => {
    poolStats.totalConnections++;
    // 只在详细调试模式下输出
    if (process.env.DB_DEBUG === 'true') {
        console.log(`📊 新建数据库连接 #${connection.threadId}, 总连接数: ${poolStats.totalConnections}`);
    }
});

pool.on('acquire', (connection) => {
    poolStats.activeConnections++;
    // 移除获取连接的日志输出
});

pool.on('release', (connection) => {
    poolStats.activeConnections = Math.max(0, poolStats.activeConnections - 1);
    // 移除释放连接的日志输出
});

pool.on('enqueue', () => {
    poolStats.queuedRequests++;
    // 只在队列长度超过阈值时警告
    if (poolStats.queuedRequests > 50) {
        console.warn(`⚠️ 数据库连接队列过长: ${poolStats.queuedRequests}`);
    }
});

// 定期输出连接池状态（只在详细调试模式下，每10分钟）
if (process.env.DB_DEBUG === 'true') {
    setInterval(() => {
        logPoolStatus();
    }, 10 * 60 * 1000);
}

/**
 * 记录连接池状态
 */
function logPoolStatus() {
    const status = getPoolStatus();
    console.log('📊 数据库连接池状态报告:');
    console.log(`   - 总连接数: ${status.totalConnections}`);
    console.log(`   - 活跃连接: ${status.activeConnections}`);
    console.log(`   - 空闲连接: ${status.idleConnections}`);
    console.log(`   - 队列请求: ${status.queuedRequests}`);
    console.log(`   - 总查询数: ${status.totalQueries}`);
    console.log(`   - 失败查询: ${status.failedQueries}`);
    console.log(`   - 平均查询时间: ${status.avgQueryTime}ms`);
    console.log(`   - 连接池利用率: ${status.utilizationRate}%`);
}

/**
 * 获取连接池状态
 */
function getPoolStatus() {
    const poolInfo = pool.pool;
    return {
        totalConnections: poolStats.totalConnections,
        activeConnections: poolStats.activeConnections,
        idleConnections: poolInfo ? (poolInfo._freeConnections ? poolInfo._freeConnections.length : 0) : 0,
        queuedRequests: poolInfo ? (poolInfo._connectionQueue ? poolInfo._connectionQueue.length : 0) : 0,
        totalQueries: poolStats.totalQueries,
        failedQueries: poolStats.failedQueries,
        avgQueryTime: poolStats.avgQueryTime,
        utilizationRate: poolStats.totalConnections > 0 ?
            Math.round((poolStats.activeConnections / poolConfig.connectionLimit) * 100) : 0,
        maxConnections: poolConfig.connectionLimit,
        maxQueue: poolConfig.queueLimit
    };
}

/**
 * 测试数据库连接
 */
async function testConnection() {
    const startTime = Date.now();
    let connection = null;

    try {
        connection = await pool.getConnection();

        // 执行简单查询测试
        await connection.execute('SELECT 1 as test');

        const responseTime = Date.now() - startTime;
        console.log(`✅ 数据库连接测试成功 (响应时间: ${responseTime}ms)`);

        return true;
    } catch (error) {
        const responseTime = Date.now() - startTime;
        console.error(`❌ 数据库连接失败 (耗时: ${responseTime}ms):`, error.message);
        poolStats.failedQueries++;
        return false;
    } finally {
        if (connection) {
            connection.release();
        }
    }
}

/**
 * 执行SQL查询（带性能监控）
 * @param {string} sql SQL语句
 * @param {Array} params 参数
 * @param {Object} options 查询选项
 * @returns {Promise} 查询结果
 */
async function query(sql, params = [], options = {}) {
    const startTime = Date.now();
    const queryId = Math.random().toString(36).substr(2, 9);

    // 记录查询开始（只在详细调试模式下）
    if (process.env.DB_DEBUG === 'true' || options.debug) {
        console.log(`🔍 [${queryId}] 开始执行查询:`, sql.substring(0, 100) + (sql.length > 100 ? '...' : ''));
    }

    let connection = null;

    try {
        // 获取连接
        connection = await pool.getConnection();

        // 执行查询
        const [rows, fields] = await connection.execute(sql, params);

        // 计算查询时间
        const queryTime = Date.now() - startTime;

        // 更新统计信息
        poolStats.totalQueries++;
        poolStats.avgQueryTime = Math.round(
            (poolStats.avgQueryTime * (poolStats.totalQueries - 1) + queryTime) / poolStats.totalQueries
        );

        // 记录慢查询（超过1秒）
        if (queryTime > 1000) {
            console.warn(`🐌 [${queryId}] 慢查询警告 (${queryTime}ms):`, {
                sql: sql.substring(0, 200),
                params: params.length > 0 ? `${params.length} 个参数` : '无参数',
                affectedRows: rows.affectedRows || rows.length || 0
            });
        } else if (process.env.DB_DEBUG === 'true' || options.debug) {
            console.log(`✅ [${queryId}] 查询完成 (${queryTime}ms), 返回 ${rows.length || rows.affectedRows || 0} 条记录`);
        }

        return rows;

    } catch (error) {
        const queryTime = Date.now() - startTime;
        poolStats.failedQueries++;

        console.error(`❌ [${queryId}] SQL查询失败 (${queryTime}ms):`, {
            error: error.message,
            sql: sql.substring(0, 200),
            params: params.length > 0 ? `${params.length} 个参数` : '无参数',
            code: error.code,
            errno: error.errno
        });

        // 根据错误类型提供更友好的错误信息
        if (error.code === 'ER_DUP_ENTRY') {
            throw new Error('数据重复，请检查唯一性约束');
        } else if (error.code === 'ER_NO_SUCH_TABLE') {
            throw new Error('数据表不存在');
        } else if (error.code === 'ER_BAD_FIELD_ERROR') {
            throw new Error('字段不存在或字段名错误');
        } else if (error.code === 'ECONNREFUSED') {
            throw new Error('数据库连接被拒绝，请检查数据库服务状态');
        } else if (error.code === 'ETIMEDOUT') {
            throw new Error('数据库连接超时，请稍后重试');
        }

        throw error;
    } finally {
        if (connection) {
            connection.release();
        }
    }
}

/**
 * 执行事务
 * @param {Function} transactionFn 事务函数
 * @returns {Promise} 事务结果
 */
async function transaction(transactionFn) {
    const startTime = Date.now();
    const transactionId = Math.random().toString(36).substr(2, 9);
    let connection = null;

    try {
        connection = await pool.getConnection();
        await connection.beginTransaction();

        // 只在详细调试模式下记录事务开始
        if (process.env.DB_DEBUG === 'true') {
            console.log(`🔄 [${transactionId}] 开始事务`);
        }

        // 执行事务函数
        const result = await transactionFn(connection);

        await connection.commit();

        const transactionTime = Date.now() - startTime;
        // 只在详细调试模式下记录事务成功
        if (process.env.DB_DEBUG === 'true') {
            console.log(`✅ [${transactionId}] 事务提交成功 (${transactionTime}ms)`);
        }

        return result;

    } catch (error) {
        const transactionTime = Date.now() - startTime;

        if (connection) {
            try {
                await connection.rollback();
                // 只在详细调试模式下记录回滚成功
                if (process.env.DB_DEBUG === 'true') {
                    console.log(`🔙 [${transactionId}] 事务回滚成功 (${transactionTime}ms)`);
                }
            } catch (rollbackError) {
                console.error(`❌ [${transactionId}] 事务回滚失败:`, rollbackError.message);
            }
        }

        console.error(`❌ [${transactionId}] 事务执行失败 (${transactionTime}ms):`, error.message);
        throw error;

    } finally {
        if (connection) {
            connection.release();
        }
    }
}

/**
 * 插入上传图片记录
 * @param {Object} imageData 图片数据
 * @param {string} imageData.imageName 图片名称
 * @param {string} imageData.imagePath 图片路径
 * @param {string} imageData.cartonNumber 识别结果
 * @param {string} imageData.username 用户名（可选）
 * @returns {Promise} 插入结果
 */
async function insertUploadImage(imageData) {
    const { imageName, imagePath, cartonNumber, username } = imageData;

    // 尝试插入包含Username字段的记录
    let sql, params;

    if (username) {
        // 尝试使用Username字段
        sql = `
            INSERT INTO UploadImage (ImageName, ImagePath, CartonNumber, Username, UploadDate)
            VALUES (?, ?, ?, ?, NOW())
        `;
        params = [imageName, imagePath, cartonNumber, username];
    } else {
        // 不包含Username字段的兼容模式
        sql = `
            INSERT INTO UploadImage (ImageName, ImagePath, CartonNumber, UploadDate)
            VALUES (?, ?, ?, NOW())
        `;
        params = [imageName, imagePath, cartonNumber];
    }

    try {
        const result = await query(sql, params);
        return result;
    } catch (error) {
        // 如果插入失败且是因为Username字段不存在，回退到不包含Username的插入
        if (username && error.message.includes('Unknown column')) {
            console.log('⚠️ UploadImage表中没有Username字段，回退到兼容模式');
            const fallbackSql = `
                INSERT INTO UploadImage (ImageName, ImagePath, CartonNumber, UploadDate)
                VALUES (?, ?, ?, NOW())
            `;
            const fallbackParams = [imageName, imagePath, cartonNumber];
            return await query(fallbackSql, fallbackParams);
        }

        console.error('保存图片记录失败:', error.message);
        throw error;
    }
}

/**
 * 更新图片路径和文件名
 * @param {string} oldImagePath 旧的图片路径
 * @param {string} newImagePath 新的图片路径
 * @param {string} newImageName 新的文件名
 * @returns {Promise} 更新结果
 */
async function updateImagePath(oldImagePath, newImagePath, newImageName) {
    const sql = `
        UPDATE UploadImage
        SET ImagePath = ?, ImageName = ?
        WHERE ImagePath = ?
    `;

    const params = [newImagePath, newImageName, oldImagePath];

    try {
        const result = await query(sql, params);
        return result;
    } catch (error) {
        console.error('更新图片路径失败:', error.message);
        throw error;
    }
}



/**
 * 插入装柜放船样登记表数据
 * @param {Array} registrationData 登记表数据数组
 * @returns {Promise} 插入结果
 */
async function insertRegistrationForm(registrationData) {
    if (!Array.isArray(registrationData) || registrationData.length === 0) {
        throw new Error('登记表数据不能为空');
    }

    const sql = `
        INSERT INTO Registrationform (Date, OrderNumber, CategoryName, SalesmanName, IsPlace, Comment, UploadDate)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    try {
        const results = [];

        // 批量插入数据
        for (const row of registrationData) {
            const { Date: dateValue, OrderNumber, CategoryName, SalesmanName, IsPlace, Comment, UploadDate } = row;

            // 验证必填字段
            if (!dateValue || !OrderNumber) {
                continue;
            }

            // 处理上传时间，如果没有提供则使用当前时间
            const uploadDateTime = UploadDate ? new Date(UploadDate) : new Date();

            const params = [
                dateValue || null,
                OrderNumber || null,
                CategoryName || null,
                SalesmanName || null,
                IsPlace || null,
                Comment || null,
                uploadDateTime
            ];

            const result = await query(sql, params);
            results.push(result);

        }

        return results;

    } catch (error) {
        console.error('保存登记表记录失败:', error.message);
        throw error;
    }
}

/**
 * 更新登记表中的图片路径
 * 注意：registrationform表没有ImagePath字段，此函数已废弃
 * @param {string} oldImagePath 旧的图片路径
 * @param {string} newImagePath 新的图片路径
 * @returns {Promise} 更新结果
 */
async function updateRegistrationFormImagePath(oldImagePath, newImagePath) {
    // 登记表没有ImagePath字段，不执行任何操作
    // 移除日志输出，避免过多信息
    return { affectedRows: 0, message: '表中没有ImagePath字段' };
}



/**
 * 插入装柜信息表数据
 * @param {Object} cartonData 装柜信息数据
 * @returns {Promise} 插入结果
 */
async function insertCartonInfo(cartonData) {
    const {
        Date: dateValue,
        SalesmanName,
        CartonNumber,
        SealNumber,
        orderData,
        TotalPiece,
        TotalWeight,
        UploadDate
    } = cartonData;

    try {
        const results = [];

        // 处理上传时间，如果没有提供则使用当前时间
        const uploadDateTime = UploadDate ? new Date(UploadDate) : new Date();

        // 为每个订单记录插入数据
        if (Array.isArray(orderData) && orderData.length > 0) {
            const sql = `
                INSERT INTO cartoninfo (Date, SalesmanName, CartonNumber, SealNumber,
                                      OrderNumber, CategoryName, Piece, Weight,
                                      TotalPiece, TotalWeight, UploadDate)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            for (const order of orderData) {
                const params = [
                    dateValue || null,
                    SalesmanName || null,
                    CartonNumber || null,
                    SealNumber || null,
                    order.OrderNumber || null,
                    order.CategoryName || null,
                    order.Piece || null,
                    order.Weight || null,
                    TotalPiece || null,
                    TotalWeight || null,
                    uploadDateTime
                ];

                const result = await query(sql, params);
                results.push(result);
            }
        } else {
            // 如果没有订单数据，只保存基本信息
            const sql = `
                INSERT INTO cartoninfo (Date, SalesmanName, CartonNumber, SealNumber,
                                      TotalPiece, TotalWeight, UploadDate)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            `;

            const params = [
                dateValue || null,
                SalesmanName || null,
                CartonNumber || null,
                SealNumber || null,
                TotalPiece || null,
                TotalWeight || null,
                uploadDateTime
            ];

            const result = await query(sql, params);
            results.push(result);

        }

        return results;

    } catch (error) {
        console.error('保存装柜信息记录失败:', error.message);
        throw error;
    }
}

/**
 * 更新装柜信息表中的图片路径
 * 注意：cartoninfo表没有ImagePath字段，此函数已废弃
 * @param {string} oldImagePath 旧的图片路径
 * @param {string} newImagePath 新的图片路径
 * @returns {Promise} 更新结果
 */
async function updateCartonInfoImagePath(oldImagePath, newImagePath) {
    // 装柜信息表没有ImagePath字段，不执行任何操作
    // 移除日志输出，避免过多信息
    return { affectedRows: 0, message: '表中没有ImagePath字段' };
}



/**
 * 连接池健康检查
 */
async function healthCheck() {
    const status = getPoolStatus();
    const isHealthy = {
        database: false,
        connectionPool: false,
        performance: false,
        overall: false
    };

    try {
        // 测试数据库连接
        isHealthy.database = await testConnection();

        // 检查连接池状态
        isHealthy.connectionPool = (
            status.activeConnections < status.maxConnections * 0.9 && // 活跃连接不超过90%
            status.queuedRequests < status.maxQueue * 0.8 && // 队列请求不超过80%
            status.failedQueries / Math.max(status.totalQueries, 1) < 0.05 // 失败率低于5%
        );

        // 检查性能指标
        isHealthy.performance = (
            status.avgQueryTime < 1000 && // 平均查询时间小于1秒
            status.utilizationRate < 90 // 连接池利用率小于90%
        );

        isHealthy.overall = isHealthy.database && isHealthy.connectionPool && isHealthy.performance;

        return {
            healthy: isHealthy.overall,
            checks: isHealthy,
            status: status,
            timestamp: new Date().toISOString()
        };

    } catch (error) {
        console.error('健康检查失败:', error.message);
        return {
            healthy: false,
            error: error.message,
            status: status,
            timestamp: new Date().toISOString()
        };
    }
}

/**
 * 重置连接池统计信息
 */
function resetPoolStats() {
    const oldStats = { ...poolStats };
    poolStats = {
        totalConnections: 0,
        activeConnections: 0,
        idleConnections: 0,
        queuedRequests: 0,
        totalQueries: 0,
        failedQueries: 0,
        avgQueryTime: 0,
        lastResetTime: new Date()
    };

    console.log('📊 连接池统计信息已重置');
    return oldStats;
}

/**
 * 强制清理空闲连接
 */
async function cleanupIdleConnections() {
    try {
        // 这是一个内部方法，用于清理长时间空闲的连接
        if (pool.pool && pool.pool._purgeTimer) {
            clearTimeout(pool.pool._purgeTimer);
            pool.pool._purgeTimer = null;
        }

        console.log('🧹 已触发空闲连接清理');
        return true;
    } catch (error) {
        console.error('清理空闲连接失败:', error.message);
        return false;
    }
}

/**
 * 关闭数据库连接池
 */
async function closePool() {
    try {
        console.log('🔄 正在关闭数据库连接池...');

        // 记录最终统计信息
        const finalStats = getPoolStatus();
        console.log('📊 最终连接池统计:', finalStats);

        await pool.end();
        console.log('✅ 数据库连接池已关闭');
    } catch (error) {
        console.error('❌ 关闭数据库连接池失败:', error.message);
    }
}

// 进程退出时自动关闭连接池
process.on('SIGINT', async () => {
    console.log('\n🛑 收到退出信号，正在关闭数据库连接池...');
    await closePool();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 收到终止信号，正在关闭数据库连接池...');
    await closePool();
    process.exit(0);
});

module.exports = {
    pool,
    testConnection,
    query,
    transaction,
    insertUploadImage,
    updateImagePath,
    insertRegistrationForm,
    updateRegistrationFormImagePath,
    insertCartonInfo,
    updateCartonInfoImagePath,
    closePool,
    getPoolStatus,
    healthCheck,
    resetPoolStats,
    cleanupIdleConnections,
    logPoolStatus
};
